apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: my-app-role
  labels:
    app: my-app
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: my-app-rolebinding
  labels:
    app: my-app
subjects:
- kind: ServiceAccount
  name: my-app-sa
  namespace: my-app-prod
roleRef:
  kind: Role
  name: my-app-role
  apiGroup: rbac.authorization.k8s.io
