# Google Cloud Platform Configuration
GCP_PROJECT_ID=your-gcp-project-id
GCP_REGION=us-central1
GCP_ZONE=us-central1-a
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Pub/Sub Configuration
PUBSUB_TOPIC_CONVERT_KUSTOMIZE=convert-kustomize
PUBSUB_TOPIC_PARSED_SPEC=parsed-spec
PUBSUB_TOPIC_CONTEXT_DOCS=context-docs
PUBSUB_TOPIC_DRAFT_CHART=draft-chart
PUBSUB_TOPIC_VALIDATED_CHART=validated-chart
PUBSUB_TOPIC_FINAL_RESULT=final-result
PUBSUB_SUBSCRIPTION_PREFIX=kube2helm

# Cloud Storage Configuration
GCS_BUCKET_CHARTS=kube2helm-charts
GCS_BUCKET_ARTIFACTS=kube2helm-artifacts
GCS_BUCKET_TEMP=kube2helm-temp

# Firestore Configuration
FIRESTORE_DATABASE=(default)
FIRESTORE_COLLECTION_JOBS=conversion-jobs
FIRESTORE_COLLECTION_CHARTS=helm-charts
FIRESTORE_COLLECTION_LOGS=agent-logs

# BigQuery Configuration
BIGQUERY_DATASET=kube2helm_analytics
BIGQUERY_TABLE_CONVERSIONS=conversions
BIGQUERY_TABLE_METRICS=metrics
BIGQUERY_TABLE_ERRORS=errors

# Vertex AI Configuration
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_MODEL=gemini-1.5-pro
VERTEX_AI_ENDPOINT=your-vertex-ai-endpoint

# Repository Configuration
GITHUB_TOKEN=your-github-token
GITHUB_WEBHOOK_SECRET=your-webhook-secret
GITLAB_TOKEN=your-gitlab-token
**********************your-gitlab-webhook-secret

# Kubernetes Configuration
KUBECONFIG=path/to/kubeconfig
K8S_NAMESPACE=kube2helm
TEST_CLUSTER_ENDPOINT=https://your-test-cluster
TEST_CLUSTER_TOKEN=your-cluster-token

# Helm Configuration
HELM_REPOSITORY_URL=https://your-helm-repo
HELM_REPOSITORY_USERNAME=your-helm-username
HELM_REPOSITORY_PASSWORD=your-helm-password

# Monitoring and Alerting
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
SLACK_CHANNEL=#kube2helm-alerts
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-email-password
ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# Application Configuration
LOG_LEVEL=INFO
DEBUG=false
ENVIRONMENT=development
API_HOST=0.0.0.0
API_PORT=8000
WORKER_CONCURRENCY=4
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_SECONDS=5

# Security Configuration
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Timeouts (in seconds)
HTTP_TIMEOUT=30
PUBSUB_TIMEOUT=60
VERTEX_AI_TIMEOUT=120
HELM_TIMEOUT=300
KUBECTL_TIMEOUT=180

# Feature Flags
ENABLE_AUTO_DEPLOYMENT=false
ENABLE_SLACK_NOTIFICATIONS=true
ENABLE_EMAIL_NOTIFICATIONS=false
ENABLE_BIGQUERY_LOGGING=true
ENABLE_PROMETHEUS_METRICS=true

# Development Configuration
DEV_SKIP_AUTH=false
DEV_MOCK_VERTEX_AI=false
DEV_LOCAL_STORAGE=false
