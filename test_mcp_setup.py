#!/usr/bin/env python3
"""Test script to verify MCP setup and configuration."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from shared.config.mcp_servers import mcp_config
from shared.utils.mcp_client import MCPClientManager, MCP_AVAILABLE

# Simple logger for testing
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_mcp_configuration():
    """Test MCP configuration loading."""
    print("🔧 Testing MCP Configuration...")
    
    try:
        # Test configuration loading
        print(f"✅ MCP configuration loaded successfully")
        print(f"   - Workspace root: {mcp_config.workspace_root}")
        print(f"   - Max connections: {mcp_config.max_connections}")
        print(f"   - Connection timeout: {mcp_config.connection_timeout}")
        
        # Test server configurations
        print(f"\n📋 Available MCP Servers:")
        servers = [
            "git", "github", "gitlab", "kubernetes", "filesystem",
            "memory", "time", "sequential_thinking"
        ]
        
        for server_name in servers:
            server_config = getattr(mcp_config.servers, f"{server_name}_server", None)
            if server_config:
                status = "✅ Enabled" if server_config.enabled else "❌ Disabled"
                print(f"   - {server_name}: {status}")
                print(f"     Command: {' '.join(server_config.command)}")
            else:
                print(f"   - {server_name}: ❌ Not configured")
        
        # Test agent mappings
        print(f"\n🤖 Agent MCP Server Mappings:")
        agents = ["watcher", "parser", "doc_reference", "translator"]
        
        for agent_name in agents:
            servers = mcp_config.get_servers_for_agent(agent_name)
            print(f"   - {agent_name}_agent: {len(servers)} servers")
            for server in servers:
                print(f"     * {server.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP configuration test failed: {e}")
        return False


async def test_mcp_client():
    """Test MCP client functionality."""
    print(f"\n🔌 Testing MCP Client...")
    
    try:
        # Check MCP availability
        print(f"   - MCP Package Available: {'✅ Yes' if MCP_AVAILABLE else '❌ No'}")
        
        # Create client manager
        client = MCPClientManager()
        print(f"   - Client Manager Created: ✅")
        
        # Test a simple tool call (will be mocked if MCP not available)
        try:
            result = await client.call_tool(
                server_name="time",
                tool_name="get_current_time",
                arguments={}
            )
            print(f"   - Test Tool Call: ✅ Success")
        except Exception as e:
            print(f"   - Test Tool Call: ❌ {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP client test failed: {e}")
        return False


async def test_watcher_agent_requirements():
    """Test that Watcher Agent has all required MCP servers."""
    print(f"\n👁️  Testing Watcher Agent MCP Requirements...")
    
    try:
        required_servers = mcp_config.get_servers_for_agent("watcher")
        print(f"   - Required servers: {len(required_servers)}")
        
        missing_servers = []
        for server in required_servers:
            if not server.enabled:
                missing_servers.append(server.name)
        
        if missing_servers:
            print(f"   - Missing/Disabled servers: ❌ {', '.join(missing_servers)}")
            return False
        else:
            print(f"   - All required servers available: ✅")
            return True
            
    except Exception as e:
        print(f"❌ Watcher agent requirements test failed: {e}")
        return False


async def main():
    """Run all MCP tests."""
    print("🚀 Starting MCP Setup Tests...\n")
    
    tests = [
        ("Configuration", test_mcp_configuration),
        ("Client", test_mcp_client),
        ("Watcher Agent Requirements", test_watcher_agent_requirements),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   - {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MCP setup is ready.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        if not MCP_AVAILABLE:
            print("\n💡 To install MCP package: pip install mcp")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
