version: '3.8'

services:
  # Watcher Agent - Monitors repository changes
  watcher:
    build:
      context: .
      target: watcher
    environment:
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json
    volumes:
      - ./credentials.json:/app/credentials.json:ro
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Parser Agent - Processes Kustomize configurations
  parser:
    build:
      context: .
      target: parser
    environment:
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json
    volumes:
      - ./credentials.json:/app/credentials.json:ro
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - watcher

  # DocReference Agent - Retrieves documentation
  doc-reference:
    build:
      context: .
      target: doc-reference
    environment:
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json
    volumes:
      - ./credentials.json:/app/credentials.json:ro
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - parser

  # Translator Agent - AI-powered conversion
  translator:
    build:
      context: .
      target: translator
    environment:
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json
      - VERTEX_AI_LOCATION=${VERTEX_AI_LOCATION}
      - VERTEX_AI_MODEL=${VERTEX_AI_MODEL}
    volumes:
      - ./credentials.json:/app/credentials.json:ro
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - doc-reference

  # Linter Agent - Validates Helm charts
  linter:
    build:
      context: .
      target: linter
    environment:
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json
    volumes:
      - ./credentials.json:/app/credentials.json:ro
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - translator

  # Diff Validator - Compares outputs
  diff-validator:
    build:
      context: .
      target: diff-validator
    environment:
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json
    volumes:
      - ./credentials.json:/app/credentials.json:ro
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - linter

  # Packager Agent - Packages and creates PRs
  packager:
    build:
      context: .
      target: packager
    environment:
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      - GITLAB_TOKEN=${GITLAB_TOKEN}
    volumes:
      - ./credentials.json:/app/credentials.json:ro
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - diff-validator

  # Feedback Agent - Monitoring and notifications
  feedback:
    build:
      context: .
      target: feedback
    environment:
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json
      - SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}
      - EMAIL_SMTP_HOST=${EMAIL_SMTP_HOST}
      - EMAIL_USERNAME=${EMAIL_USERNAME}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
    volumes:
      - ./credentials.json:/app/credentials.json:ro
      - ./logs:/app/logs
    restart: unless-stopped

  # Deploy Test Agent - Optional test deployment
  deploy-test:
    build:
      context: .
      target: deploy-test
    environment:
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json
      - KUBECONFIG=/app/kubeconfig
      - TEST_CLUSTER_ENDPOINT=${TEST_CLUSTER_ENDPOINT}
    volumes:
      - ./credentials.json:/app/credentials.json:ro
      - ./kubeconfig:/app/kubeconfig:ro
      - ./logs:/app/logs
    restart: unless-stopped
    profiles:
      - testing

  # Development services
  dev:
    build:
      context: .
      target: development
    environment:
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    volumes:
      - .:/app
      - ./credentials.json:/app/credentials.json:ro
    ports:
      - "8000:8000"
      - "8001:8001"
      - "8002:8002"
    command: ["python", "-m", "uvicorn", "agents.watcher.api:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    profiles:
      - development

volumes:
  logs:
    driver: local

networks:
  default:
    name: kube2helm-network
