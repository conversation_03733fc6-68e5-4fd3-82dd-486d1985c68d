# Kube2Helm Agent System

A comprehensive multi-agent system for converting Kustomize configurations to Helm charts with automated validation, testing, and deployment.

## Architecture Overview

```
┌───────────────────────┐
│  Source Control Repo  │  (kustomization.yaml + bases)
└─────────┬─────────────┘
          │ ① Change event (push / MR)
┌─────────▼─────────────┐
│  Watcher Agent        │  — Cloud Build / Cloud Functions; enqueues job
└─────────┬─────────────┘
          │ Pub/Sub "convert‑kustomize" message
┌─────────▼─────────────┐
│  Parser Agent         │  — decodes Kustomize, produces AST + rendered YAML
└─────────┬─────────────┘
          │ ② ParsedSpec JSON
┌─────────▼─────────────┐
│  DocReference Agent   │  — queries MCP docs index for best‑practice chunks
└─────────┬─────────────┘
          │ ③ ContextDocs
┌─────────▼─────────────┐
│  Translator Agent     │  — Vertex AI LLM; turns ParsedSpec+Context into Helm
└─────────┬─────────────┘
          │ ④ Draft Chart
┌─────────▼─────────────┐
│  Linter/UnitTest Agent│  — helm lint, kube‑val, chart‑testing, OPA
└─────────┬─────────────┘
          │ ⑤ Validated Chart / Issues
┌─────────▼─────────────┐
│  Diff & Validator     │  — diff(rendered Helm) ≟ kustomize build output
└─────────┬─────────────┘
          │ ⑥ Success / Fix‑request
┌─────────▼─────────────┐
│  Packager & PR Agent  │  — pushes `.tgz`, opens MR, posts summary
└──────┬────────┬───────┘
       │        │
       │        └──> Feedback Agent — logs to BigQuery, Slack, etc.
       └────────────> Optional Deploy‑to‑Test‑Cluster Agent
```

## Components

### Core Agents
- **Watcher Agent**: Monitors repository changes and triggers pipeline
- **Parser Agent**: Processes Kustomize configurations and generates AST
- **DocReference Agent**: Retrieves best-practice documentation
- **Translator Agent**: AI-powered Kustomize-to-Helm conversion
- **Linter/UnitTest Agent**: Validates generated Helm charts
- **Diff & Validator**: Compares outputs for correctness
- **Packager & PR Agent**: Packages and deploys results

### Supporting Services
- **Feedback Agent**: Monitoring and notifications
- **Deploy-to-Test-Cluster Agent**: Optional testing deployment

## Technology Stack

- **Language**: Python 3.11+
- **Cloud Platform**: Google Cloud Platform
- **Messaging**: Google Cloud Pub/Sub
- **AI/ML**: Vertex AI (Gemini/PaLM)
- **Storage**: Google Cloud Storage, Firestore
- **Monitoring**: Google Cloud Logging, BigQuery
- **Container Runtime**: Cloud Run, Cloud Functions
- **CI/CD**: Cloud Build

## Project Structure

```
kube2helm_agent/
├── agents/                 # Individual agent implementations
│   ├── watcher/           # Repository monitoring
│   ├── parser/            # Kustomize processing
│   ├── doc_reference/     # Documentation retrieval
│   ├── translator/        # AI-powered conversion
│   ├── linter/            # Validation and testing
│   ├── diff_validator/    # Output comparison
│   ├── packager/          # Chart packaging and PR
│   ├── feedback/          # Monitoring and alerts
│   └── deploy_test/       # Optional test deployment
├── shared/                # Common utilities and models
│   ├── models/            # Data models and schemas
│   ├── utils/             # Utility functions
│   └── config/            # Configuration management
├── infrastructure/        # Infrastructure as Code
│   ├── terraform/         # GCP resource definitions
│   └── cloudbuild/        # CI/CD configurations
├── tests/                 # Test suites
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   └── e2e/               # End-to-end tests
└── docs/                  # Documentation
    ├── api/               # API documentation
    ├── deployment/        # Deployment guides
    └── architecture/      # Architecture documentation
```

## Getting Started

1. **Prerequisites**
   - Python 3.11+
   - Google Cloud SDK
   - Docker
   - Helm CLI
   - kubectl

2. **Setup**
   ```bash
   # Clone repository
   git clone <repository-url>
   cd kube2helm_agent
   
   # Install dependencies
   pip install -r requirements.txt
   
   # Configure GCP
   gcloud auth login
   gcloud config set project <your-project-id>
   ```

3. **Development**
   ```bash
   # Run tests
   pytest tests/
   
   # Start local development
   python -m agents.watcher.main
   ```

## Configuration

Environment variables and configuration files are used to manage:
- GCP project settings
- Pub/Sub topic names
- AI model configurations
- Repository access tokens
- Notification settings

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Submit a pull request

## License

[License information]
