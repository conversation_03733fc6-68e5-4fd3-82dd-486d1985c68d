apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Metadata for the kustomization
metadata:
  name: my-app-production
  annotations:
    config.kubernetes.io/local-config: "true"

# Namespace to deploy resources into
namespace: my-app-prod

# Common labels applied to all resources
commonLabels:
  app: my-application
  environment: production
  team: platform
  version: v1.2.3

# Common annotations applied to all resources
commonAnnotations:
  managed-by: kustomize
  deployment.kubernetes.io/revision: "1"

# Name prefix for all resources
namePrefix: prod-

# Name suffix for all resources
nameSuffix: -v1

# Base resources to include
resources:
  - deployment.yaml
  - service.yaml
  - configmap.yaml
  - secret.yaml
  - ingress.yaml
  - serviceaccount.yaml
  - rbac.yaml

# ConfigMap generators
configMapGenerator:
  - name: app-config
    files:
      - config/app.properties
      - config/logging.conf
    literals:
      - DATABASE_HOST=postgres.example.com
      - DATABASE_PORT=5432
      - LOG_LEVEL=INFO
      - FEATURE_FLAG_NEW_UI=true

  - name: nginx-config
    files:
      - nginx/nginx.conf
      - nginx/default.conf

# Secret generators
secretGenerator:
  - name: app-secrets
    literals:
      - DATABASE_PASSWORD=super-secret-password
      - API_KEY=abc123def456
      - JWT_SECRET=my-jwt-secret-key
    type: Opaque

  - name: tls-secret
    files:
      - tls/tls.crt
      - tls/tls.key
    type: kubernetes.io/tls

# Image transformations
images:
  - name: my-app
    newName: gcr.io/my-project/my-app
    newTag: v1.2.3
  - name: nginx
    newName: nginx
    newTag: 1.21-alpine
  - name: postgres
    newName: postgres
    newTag: 13.4

# JSON/Strategic merge patches
patchesStrategicMerge:
  - patches/deployment-patch.yaml
  - patches/service-patch.yaml

# JSON 6902 patches
patchesJson6902:
  - target:
      group: apps
      version: v1
      kind: Deployment
      name: my-app
    path: patches/deployment-replicas.yaml

  - target:
      group: v1
      kind: Service
      name: my-app-service
    path: patches/service-nodeport.yaml

# Inline patches
patches:
  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 3
    target:
      kind: Deployment
      name: my-app

  - patch: |-
      - op: add
        path: /metadata/annotations/prometheus.io~1scrape
        value: "true"
      - op: add
        path: /metadata/annotations/prometheus.io~1port
        value: "8080"
    target:
      kind: Service
      name: my-app-service

# Replica count transformer
replicas:
  - name: my-app
    count: 3
  - name: worker
    count: 2

# Resource transformers
transformers:
  - transformers/add-labels.yaml
  - transformers/set-namespace.yaml

# Generators for additional resources
generators:
  - generators/secret-generator.yaml
  - generators/configmap-generator.yaml

# Helm chart inflation (if using Helm charts as bases)
helmCharts:
  - name: postgresql
    repo: https://charts.bitnami.com/bitnami
    version: 11.9.13
    releaseName: my-postgres
    namespace: database
    valuesInline:
      auth:
        postgresPassword: my-secret-password
        database: myapp
      primary:
        persistence:
          size: 20Gi

# OpenAPI schema validation
openapi:
  path: schemas/kubernetes-openapi.json

# Build metadata
buildMetadata:
  - originAnnotations
  - transformerAnnotations
  - managedByLabel

# Variable substitutions
vars:
  - name: APP_NAME
    objref:
      kind: ConfigMap
      name: app-config
      apiVersion: v1
    fieldref:
      fieldpath: metadata.name
  
  - name: SERVICE_NAME
    objref:
      kind: Service
      name: my-app-service
      apiVersion: v1
    fieldref:
      fieldpath: metadata.name

# Inventory configuration
inventory:
  type: ConfigMap
  configMap:
    name: inventory
    namespace: my-app-prod
