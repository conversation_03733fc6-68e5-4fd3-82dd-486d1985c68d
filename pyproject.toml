[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "kube2helm-agent"
version = "0.1.0"
description = "Multi-agent system for converting Kustomize configurations to Helm charts"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "Kube2Helm Team", email = "<EMAIL>"}
]
keywords = ["kubernetes", "helm", "kustomize", "devops", "automation"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Systems Administration",
]

dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "google-cloud-pubsub>=2.18.4",
    "google-cloud-storage>=2.10.0",
    "google-cloud-firestore>=2.13.1",
    "google-cloud-logging>=3.8.0",
    "google-cloud-bigquery>=3.13.0",
    "google-cloud-aiplatform>=1.38.1",
    "kubernetes>=28.1.0",
    "pyyaml>=6.0.1",
    "GitPython>=3.1.40",
    "PyGithub>=2.1.1",
    "httpx>=0.25.2",
    "jsonschema>=4.20.0",
    "click>=8.1.7",
    "rich>=13.7.0",
    "structlog>=23.2.0",
    "tenacity>=8.2.3",
    "jinja2>=3.1.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
]

[project.urls]
Homepage = "https://github.com/kube2helm/kube2helm-agent"
Repository = "https://github.com/kube2helm/kube2helm-agent"
Documentation = "https://docs.kube2helm.dev"
"Bug Tracker" = "https://github.com/kube2helm/kube2helm-agent/issues"

[project.scripts]
kube2helm-watcher = "agents.watcher.main:main"
kube2helm-parser = "agents.parser.main:main"
kube2helm-translator = "agents.translator.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["agents*", "shared*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["agents", "shared"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "google.cloud.*",
    "kubernetes.*",
    "git.*",
    "github.*",
    "gitlab.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=agents",
    "--cov=shared",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Slow running tests",
]

[tool.coverage.run]
source = ["agents", "shared"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
