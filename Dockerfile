# Multi-stage Dockerfile for Kube2Helm agents

FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# Install kubectl
RUN curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" \
    && install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl \
    && rm kubectl

# Install Helm
RUN curl https://baltocdn.com/helm/signing.asc | gpg --dearmor | tee /usr/share/keyrings/helm.gpg > /dev/null \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main" | tee /etc/apt/sources.list.d/helm-stable-debian.list \
    && apt-get update \
    && apt-get install helm \
    && rm -rf /var/lib/apt/lists/*

# Install kustomize
RUN curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash \
    && mv kustomize /usr/local/bin/

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Change ownership to app user
RUN chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Default command (can be overridden)
CMD ["python", "-m", "agents.watcher.main"]

# Development stage
FROM base as development

USER root

# Install development dependencies
RUN pip install --no-cache-dir \
    pytest \
    pytest-asyncio \
    pytest-mock \
    pytest-cov \
    black \
    isort \
    flake8 \
    mypy \
    pre-commit

USER appuser

# Production stage
FROM base as production

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Watcher agent stage
FROM base as watcher
CMD ["python", "-m", "agents.watcher.main"]

# Parser agent stage
FROM base as parser
CMD ["python", "-m", "agents.parser.main"]

# DocReference agent stage
FROM base as doc-reference
CMD ["python", "-m", "agents.doc_reference.main"]

# Translator agent stage
FROM base as translator
CMD ["python", "-m", "agents.translator.main"]

# Linter agent stage
FROM base as linter
CMD ["python", "-m", "agents.linter.main"]

# Diff validator stage
FROM base as diff-validator
CMD ["python", "-m", "agents.diff_validator.main"]

# Packager agent stage
FROM base as packager
CMD ["python", "-m", "agents.packager.main"]

# Feedback agent stage
FROM base as feedback
CMD ["python", "-m", "agents.feedback.main"]

# Deploy test agent stage
FROM base as deploy-test
CMD ["python", "-m", "agents.deploy_test.main"]
