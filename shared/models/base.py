"""Base models and common data structures."""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class JobStatus(str, Enum):
    """Status of a conversion job."""
    
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentType(str, Enum):
    """Types of agents in the system."""
    
    WATCHER = "watcher"
    PARSER = "parser"
    DOC_REFERENCE = "doc_reference"
    TRANSLATOR = "translator"
    LINTER = "linter"
    DIFF_VALIDATOR = "diff_validator"
    PACKAGER = "packager"
    FEEDBACK = "feedback"
    DEPLOY_TEST = "deploy_test"


class Severity(str, Enum):
    """Severity levels for issues and logs."""
    
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class BaseMessage(BaseModel):
    """Base class for all Pub/Sub messages."""
    
    id: UUID = Field(default_factory=uuid4, description="Unique message ID")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")
    job_id: UUID = Field(..., description="Associated job ID")
    agent_type: AgentType = Field(..., description="Agent that created this message")
    correlation_id: Optional[str] = Field(default=None, description="Correlation ID for tracing")


class RepositoryInfo(BaseModel):
    """Information about a source repository."""
    
    url: str = Field(..., description="Repository URL")
    branch: str = Field(default="main", description="Branch name")
    commit_sha: str = Field(..., description="Commit SHA")
    author: str = Field(..., description="Commit author")
    message: str = Field(..., description="Commit message")
    timestamp: datetime = Field(..., description="Commit timestamp")
    paths_changed: List[str] = Field(default_factory=list, description="Changed file paths")


class KustomizeResource(BaseModel):
    """Represents a Kustomize resource."""
    
    api_version: str = Field(..., description="Kubernetes API version")
    kind: str = Field(..., description="Kubernetes resource kind")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Resource metadata")
    spec: Dict[str, Any] = Field(default_factory=dict, description="Resource specification")
    status: Optional[Dict[str, Any]] = Field(default=None, description="Resource status")


class ParsedKustomization(BaseModel):
    """Parsed Kustomization configuration."""
    
    api_version: str = Field(default="kustomize.config.k8s.io/v1beta1")
    kind: str = Field(default="Kustomization")
    metadata: Dict[str, Any] = Field(default_factory=dict)
    resources: List[str] = Field(default_factory=list, description="Resource file paths")
    bases: List[str] = Field(default_factory=list, description="Base directories")
    patches: List[Dict[str, Any]] = Field(default_factory=list, description="Patches to apply")
    images: List[Dict[str, Any]] = Field(default_factory=list, description="Image transformations")
    config_map_generator: List[Dict[str, Any]] = Field(
        default_factory=list, 
        description="ConfigMap generators"
    )
    secret_generator: List[Dict[str, Any]] = Field(
        default_factory=list, 
        description="Secret generators"
    )
    namespace: Optional[str] = Field(default=None, description="Target namespace")
    name_prefix: Optional[str] = Field(default=None, description="Name prefix")
    name_suffix: Optional[str] = Field(default=None, description="Name suffix")
    common_labels: Dict[str, str] = Field(default_factory=dict, description="Common labels")
    common_annotations: Dict[str, str] = Field(
        default_factory=dict, 
        description="Common annotations"
    )


class HelmChart(BaseModel):
    """Represents a Helm chart."""
    
    name: str = Field(..., description="Chart name")
    version: str = Field(default="0.1.0", description="Chart version")
    description: str = Field(default="", description="Chart description")
    api_version: str = Field(default="v2", description="Helm API version")
    app_version: Optional[str] = Field(default=None, description="Application version")
    keywords: List[str] = Field(default_factory=list, description="Chart keywords")
    home: Optional[str] = Field(default=None, description="Chart home URL")
    sources: List[str] = Field(default_factory=list, description="Source URLs")
    maintainers: List[Dict[str, str]] = Field(
        default_factory=list, 
        description="Chart maintainers"
    )
    dependencies: List[Dict[str, Any]] = Field(
        default_factory=list, 
        description="Chart dependencies"
    )
    templates: Dict[str, str] = Field(
        default_factory=dict, 
        description="Template files (filename -> content)"
    )
    values: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Default values"
    )


class ValidationIssue(BaseModel):
    """Represents a validation issue."""
    
    severity: Severity = Field(..., description="Issue severity")
    message: str = Field(..., description="Issue message")
    file: Optional[str] = Field(default=None, description="File where issue occurred")
    line: Optional[int] = Field(default=None, description="Line number")
    column: Optional[int] = Field(default=None, description="Column number")
    rule: Optional[str] = Field(default=None, description="Validation rule")
    suggestion: Optional[str] = Field(default=None, description="Suggested fix")


class ConversionJob(BaseModel):
    """Represents a conversion job."""
    
    id: UUID = Field(default_factory=uuid4, description="Job ID")
    status: JobStatus = Field(default=JobStatus.PENDING, description="Job status")
    repository: RepositoryInfo = Field(..., description="Source repository info")
    kustomization_path: str = Field(..., description="Path to kustomization.yaml")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)
    error_message: Optional[str] = Field(default=None)
    result_chart_url: Optional[str] = Field(default=None, description="URL to generated chart")
    metrics: Dict[str, Any] = Field(default_factory=dict, description="Job metrics")


class AgentResult(BaseModel):
    """Base class for agent results."""
    
    job_id: UUID = Field(..., description="Associated job ID")
    agent_type: AgentType = Field(..., description="Agent that produced this result")
    success: bool = Field(..., description="Whether the operation was successful")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    duration_seconds: float = Field(..., description="Operation duration")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
