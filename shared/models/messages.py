"""Message models for inter-agent communication."""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import Field

from shared.models.base import (
    AgentResult,
    AgentType,
    BaseMessage,
    HelmChart,
    KustomizeResource,
    ParsedKustomization,
    RepositoryInfo,
    ValidationIssue,
)


class ConvertKustomizeMessage(BaseMessage):
    """Message to trigger Kustomize conversion."""
    
    agent_type: AgentType = Field(default=AgentType.WATCHER)
    repository: RepositoryInfo = Field(..., description="Repository information")
    kustomization_path: str = Field(..., description="Path to kustomization.yaml")
    webhook_payload: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Original webhook payload"
    )


class ParsedSpecMessage(BaseMessage):
    """Message containing parsed Kustomize specification."""
    
    agent_type: AgentType = Field(default=AgentType.PARSER)
    repository: RepositoryInfo = Field(..., description="Repository information")
    kustomization_path: str = Field(..., description="Path to kustomization.yaml")
    parsed_kustomization: ParsedKustomization = Field(
        ..., 
        description="Parsed Kustomization"
    )
    rendered_resources: List[KustomizeResource] = Field(
        default_factory=list, 
        description="Rendered Kubernetes resources"
    )
    ast_data: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Abstract syntax tree data"
    )


class ContextDocsMessage(BaseMessage):
    """Message containing documentation context."""
    
    agent_type: AgentType = Field(default=AgentType.DOC_REFERENCE)
    repository: RepositoryInfo = Field(..., description="Repository information")
    kustomization_path: str = Field(..., description="Path to kustomization.yaml")
    parsed_spec: ParsedSpecMessage = Field(..., description="Parsed specification")
    documentation_chunks: List[Dict[str, Any]] = Field(
        default_factory=list, 
        description="Relevant documentation chunks"
    )
    best_practices: List[Dict[str, Any]] = Field(
        default_factory=list, 
        description="Best practice recommendations"
    )


class DraftChartMessage(BaseMessage):
    """Message containing draft Helm chart."""
    
    agent_type: AgentType = Field(default=AgentType.TRANSLATOR)
    repository: RepositoryInfo = Field(..., description="Repository information")
    kustomization_path: str = Field(..., description="Path to kustomization.yaml")
    context_docs: ContextDocsMessage = Field(..., description="Context documentation")
    helm_chart: HelmChart = Field(..., description="Generated Helm chart")
    conversion_notes: List[str] = Field(
        default_factory=list, 
        description="Notes about the conversion process"
    )
    ai_confidence_score: float = Field(
        default=0.0, 
        description="AI confidence in the conversion (0-1)"
    )


class ValidatedChartMessage(BaseMessage):
    """Message containing validated Helm chart."""
    
    agent_type: AgentType = Field(default=AgentType.LINTER)
    repository: RepositoryInfo = Field(..., description="Repository information")
    kustomization_path: str = Field(..., description="Path to kustomization.yaml")
    draft_chart: DraftChartMessage = Field(..., description="Draft chart")
    validation_issues: List[ValidationIssue] = Field(
        default_factory=list, 
        description="Validation issues found"
    )
    lint_results: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Helm lint results"
    )
    test_results: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Chart testing results"
    )
    is_valid: bool = Field(..., description="Whether the chart is valid")


class DiffValidationMessage(BaseMessage):
    """Message containing diff validation results."""
    
    agent_type: AgentType = Field(default=AgentType.DIFF_VALIDATOR)
    repository: RepositoryInfo = Field(..., description="Repository information")
    kustomization_path: str = Field(..., description="Path to kustomization.yaml")
    validated_chart: ValidatedChartMessage = Field(..., description="Validated chart")
    kustomize_output: List[KustomizeResource] = Field(
        default_factory=list, 
        description="Original kustomize build output"
    )
    helm_output: List[KustomizeResource] = Field(
        default_factory=list, 
        description="Rendered Helm chart output"
    )
    diff_results: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Diff comparison results"
    )
    is_equivalent: bool = Field(..., description="Whether outputs are equivalent")
    differences: List[Dict[str, Any]] = Field(
        default_factory=list, 
        description="List of differences found"
    )


class FinalResultMessage(BaseMessage):
    """Message containing final conversion result."""
    
    agent_type: AgentType = Field(default=AgentType.PACKAGER)
    repository: RepositoryInfo = Field(..., description="Repository information")
    kustomization_path: str = Field(..., description="Path to kustomization.yaml")
    diff_validation: DiffValidationMessage = Field(..., description="Diff validation")
    chart_package_url: Optional[str] = Field(
        default=None, 
        description="URL to packaged chart"
    )
    pull_request_url: Optional[str] = Field(
        default=None, 
        description="URL to created pull request"
    )
    success: bool = Field(..., description="Whether conversion was successful")
    summary: str = Field(..., description="Conversion summary")
    metrics: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Conversion metrics"
    )


class FeedbackMessage(BaseMessage):
    """Message for feedback and monitoring."""
    
    agent_type: AgentType = Field(default=AgentType.FEEDBACK)
    repository: RepositoryInfo = Field(..., description="Repository information")
    final_result: FinalResultMessage = Field(..., description="Final result")
    notifications_sent: List[str] = Field(
        default_factory=list, 
        description="List of notifications sent"
    )
    metrics_logged: bool = Field(default=False, description="Whether metrics were logged")


class DeployTestMessage(BaseMessage):
    """Message for test deployment results."""
    
    agent_type: AgentType = Field(default=AgentType.DEPLOY_TEST)
    repository: RepositoryInfo = Field(..., description="Repository information")
    final_result: FinalResultMessage = Field(..., description="Final result")
    deployment_namespace: str = Field(..., description="Test deployment namespace")
    deployment_status: str = Field(..., description="Deployment status")
    test_results: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Test execution results"
    )
    cleanup_completed: bool = Field(
        default=False, 
        description="Whether cleanup was completed"
    )


# Agent result models
class WatcherResult(AgentResult):
    """Result from Watcher Agent."""
    
    agent_type: AgentType = Field(default=AgentType.WATCHER)
    repository: RepositoryInfo = Field(..., description="Repository information")
    kustomization_paths: List[str] = Field(
        default_factory=list, 
        description="Detected kustomization paths"
    )
    messages_published: int = Field(default=0, description="Number of messages published")


class ParserResult(AgentResult):
    """Result from Parser Agent."""
    
    agent_type: AgentType = Field(default=AgentType.PARSER)
    resources_parsed: int = Field(default=0, description="Number of resources parsed")
    kustomization_valid: bool = Field(..., description="Whether kustomization is valid")
    parsing_warnings: List[str] = Field(
        default_factory=list, 
        description="Parsing warnings"
    )


class DocReferenceResult(AgentResult):
    """Result from DocReference Agent."""
    
    agent_type: AgentType = Field(default=AgentType.DOC_REFERENCE)
    docs_retrieved: int = Field(default=0, description="Number of docs retrieved")
    relevance_score: float = Field(default=0.0, description="Average relevance score")


class TranslatorResult(AgentResult):
    """Result from Translator Agent."""
    
    agent_type: AgentType = Field(default=AgentType.TRANSLATOR)
    templates_generated: int = Field(default=0, description="Number of templates generated")
    ai_model_used: str = Field(..., description="AI model used for translation")
    confidence_score: float = Field(default=0.0, description="Translation confidence")


class LinterResult(AgentResult):
    """Result from Linter Agent."""
    
    agent_type: AgentType = Field(default=AgentType.LINTER)
    issues_found: int = Field(default=0, description="Number of issues found")
    critical_issues: int = Field(default=0, description="Number of critical issues")
    chart_valid: bool = Field(..., description="Whether chart is valid")


class DiffValidatorResult(AgentResult):
    """Result from Diff Validator Agent."""
    
    agent_type: AgentType = Field(default=AgentType.DIFF_VALIDATOR)
    differences_found: int = Field(default=0, description="Number of differences found")
    equivalence_score: float = Field(default=0.0, description="Equivalence score (0-1)")
    outputs_equivalent: bool = Field(..., description="Whether outputs are equivalent")


class PackagerResult(AgentResult):
    """Result from Packager Agent."""
    
    agent_type: AgentType = Field(default=AgentType.PACKAGER)
    chart_packaged: bool = Field(..., description="Whether chart was packaged")
    pr_created: bool = Field(..., description="Whether PR was created")
    chart_size_bytes: int = Field(default=0, description="Chart package size in bytes")
