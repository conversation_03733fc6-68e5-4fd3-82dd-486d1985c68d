"""Configuration for MCP (Model Context Protocol) servers."""

from typing import Dict, List, Optional
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class MCPServerConfig(BaseModel):
    """Configuration for a single MCP server."""
    
    name: str = Field(..., description="Server name")
    command: List[str] = Field(..., description="Command to start the server")
    args: List[str] = Field(default_factory=list, description="Additional arguments")
    env: Dict[str, str] = Field(default_factory=dict, description="Environment variables")
    timeout: int = Field(default=30, description="Connection timeout in seconds")
    enabled: bool = Field(default=True, description="Whether server is enabled")


class MCPServersConfig(BaseSettings):
    """Configuration for all MCP servers used by the system."""
    
    model_config = SettingsConfigDict(env_prefix="MCP_")
    
    # Git operations server
    git_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="git",
            command=["npx", "-y", "@modelcontextprotocol/server-git"],
            args=["--repository-path", "/tmp/repos"],
            env={"GIT_AUTHOR_NAME": "Kube2Helm Agent", "GIT_AUTHOR_EMAIL": "<EMAIL>"}
        )
    )
    
    # GitHub operations server
    github_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="github",
            command=["npx", "-y", "@modelcontextprotocol/server-github"],
            env={"GITHUB_PERSONAL_ACCESS_TOKEN": ""}  # Set via environment
        )
    )
    
    # GitLab operations server
    gitlab_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="gitlab",
            command=["npx", "-y", "@modelcontextprotocol/server-gitlab"],
            env={"GITLAB_PERSONAL_ACCESS_TOKEN": ""}  # Set via environment
        )
    )
    
    # Kubernetes operations server
    kubernetes_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="kubernetes",
            command=["npx", "-y", "@modelcontextprotocol/server-kubernetes"],
            env={"KUBECONFIG": "/app/kubeconfig"}
        )
    )
    
    # Filesystem operations server
    filesystem_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="filesystem",
            command=["npx", "-y", "@modelcontextprotocol/server-filesystem"],
            args=["--allowed-directory", "/tmp/workspaces"]
        )
    )
    
    # Brave search server for documentation
    brave_search_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="brave_search",
            command=["npx", "-y", "@modelcontextprotocol/server-brave-search"],
            env={"BRAVE_API_KEY": ""}  # Set via environment
        )
    )
    
    # Web scraping server for documentation
    web_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="web",
            command=["npx", "-y", "@modelcontextprotocol/server-web"],
            args=["--user-agent", "Kube2Helm-Agent/1.0"]
        )
    )
    
    # Slack notifications server
    slack_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="slack",
            command=["npx", "-y", "@modelcontextprotocol/server-slack"],
            env={"SLACK_BOT_TOKEN": ""}  # Set via environment
        )
    )
    
    # Google Drive server for documentation storage
    gdrive_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="gdrive",
            command=["npx", "-y", "@modelcontextprotocol/server-gdrive"],
            env={"GOOGLE_APPLICATION_CREDENTIALS": "/app/credentials.json"}
        )
    )
    
    # SQLite server for local data storage
    sqlite_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="sqlite",
            command=["npx", "-y", "@modelcontextprotocol/server-sqlite"],
            args=["--db-path", "/app/data/kube2helm.db"]
        )
    )
    
    # PostgreSQL server for production data storage
    postgres_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="postgres",
            command=["npx", "-y", "@modelcontextprotocol/server-postgres"],
            env={
                "POSTGRES_CONNECTION_STRING": ""  # Set via environment
            }
        )
    )
    
    # Memory server for caching
    memory_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="memory",
            command=["npx", "-y", "@modelcontextprotocol/server-memory"]
        )
    )
    
    # Sequential thinking server for complex reasoning
    sequential_thinking_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="sequential_thinking",
            command=["npx", "-y", "@modelcontextprotocol/server-sequential-thinking"]
        )
    )
    
    # Time server for scheduling and timestamps
    time_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="time",
            command=["npx", "-y", "@modelcontextprotocol/server-time"]
        )
    )
    
    # Puppeteer server for web automation
    puppeteer_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="puppeteer",
            command=["npx", "-y", "@modelcontextprotocol/server-puppeteer"]
        )
    )
    
    # Everything server for comprehensive search
    everything_server: MCPServerConfig = Field(
        default_factory=lambda: MCPServerConfig(
            name="everything",
            command=["npx", "-y", "@modelcontextprotocol/server-everything"],
            enabled=False  # Windows-only, disabled by default
        )
    )


class MCPAgentMapping(BaseModel):
    """Mapping of agents to their required MCP servers."""
    
    watcher_agent: List[str] = Field(
        default_factory=lambda: [
            "git", "github", "gitlab", "filesystem", "memory", "time", "sqlite"
        ]
    )
    
    parser_agent: List[str] = Field(
        default_factory=lambda: [
            "filesystem", "memory", "sequential_thinking", "sqlite"
        ]
    )
    
    doc_reference_agent: List[str] = Field(
        default_factory=lambda: [
            "brave_search", "web", "gdrive", "memory", "sqlite"
        ]
    )
    
    translator_agent: List[str] = Field(
        default_factory=lambda: [
            "memory", "sequential_thinking", "filesystem", "sqlite"
        ]
    )
    
    linter_agent: List[str] = Field(
        default_factory=lambda: [
            "filesystem", "kubernetes", "memory", "sqlite"
        ]
    )
    
    diff_validator_agent: List[str] = Field(
        default_factory=lambda: [
            "filesystem", "kubernetes", "memory", "sequential_thinking", "sqlite"
        ]
    )
    
    packager_agent: List[str] = Field(
        default_factory=lambda: [
            "filesystem", "github", "gitlab", "memory", "sqlite"
        ]
    )
    
    feedback_agent: List[str] = Field(
        default_factory=lambda: [
            "slack", "memory", "time", "postgres", "sqlite"
        ]
    )
    
    deploy_test_agent: List[str] = Field(
        default_factory=lambda: [
            "kubernetes", "filesystem", "memory", "time", "sqlite"
        ]
    )


class MCPConfiguration(BaseSettings):
    """Main MCP configuration."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    servers: MCPServersConfig = Field(default_factory=MCPServersConfig)
    agent_mapping: MCPAgentMapping = Field(default_factory=MCPAgentMapping)
    
    # Global MCP settings
    max_connections: int = Field(default=10, description="Maximum concurrent MCP connections")
    connection_timeout: int = Field(default=30, description="Connection timeout in seconds")
    retry_attempts: int = Field(default=3, description="Number of retry attempts")
    retry_delay: int = Field(default=5, description="Delay between retries in seconds")
    
    # Node.js and npm configuration for MCP servers
    node_path: str = Field(default="node", description="Path to Node.js executable")
    npm_path: str = Field(default="npm", description="Path to npm executable")
    npx_path: str = Field(default="npx", description="Path to npx executable")
    
    # Workspace configuration
    workspace_root: str = Field(default="/tmp/kube2helm_workspaces", description="Root workspace directory")
    temp_directory: str = Field(default="/tmp/kube2helm_temp", description="Temporary files directory")
    
    def get_servers_for_agent(self, agent_name: str) -> List[MCPServerConfig]:
        """Get MCP servers required for a specific agent.
        
        Args:
            agent_name: Name of the agent
            
        Returns:
            List of MCP server configurations
        """
        server_names = getattr(self.agent_mapping, f"{agent_name}_agent", [])
        servers = []
        
        for server_name in server_names:
            server_config = getattr(self.servers, f"{server_name}_server", None)
            if server_config and server_config.enabled:
                servers.append(server_config)
        
        return servers


# Global MCP configuration instance
mcp_config = MCPConfiguration()
