"""Configuration settings for the Kube2Helm agent system."""

from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class GCPSettings(BaseSettings):
    """Google Cloud Platform configuration."""
    
    model_config = SettingsConfigDict(env_prefix="GCP_")
    
    project_id: str = Field(..., description="GCP Project ID")
    region: str = Field(default="us-central1", description="GCP Region")
    zone: str = Field(default="us-central1-a", description="GCP Zone")
    credentials_path: Optional[str] = Field(
        default=None, 
        alias="GOOGLE_APPLICATION_CREDENTIALS",
        description="Path to service account credentials"
    )


class PubSubSettings(BaseSettings):
    """Pub/Sub configuration."""
    
    model_config = SettingsConfigDict(env_prefix="PUBSUB_")
    
    topic_convert_kustomize: str = Field(default="convert-kustomize")
    topic_parsed_spec: str = Field(default="parsed-spec")
    topic_context_docs: str = Field(default="context-docs")
    topic_draft_chart: str = Field(default="draft-chart")
    topic_validated_chart: str = Field(default="validated-chart")
    topic_final_result: str = Field(default="final-result")
    subscription_prefix: str = Field(default="kube2helm")
    timeout: int = Field(default=60, description="Pub/Sub timeout in seconds")


class StorageSettings(BaseSettings):
    """Cloud Storage configuration."""
    
    model_config = SettingsConfigDict(env_prefix="GCS_")
    
    bucket_charts: str = Field(default="kube2helm-charts")
    bucket_artifacts: str = Field(default="kube2helm-artifacts")
    bucket_temp: str = Field(default="kube2helm-temp")


class FirestoreSettings(BaseSettings):
    """Firestore configuration."""
    
    model_config = SettingsConfigDict(env_prefix="FIRESTORE_")
    
    database: str = Field(default="(default)")
    collection_jobs: str = Field(default="conversion-jobs")
    collection_charts: str = Field(default="helm-charts")
    collection_logs: str = Field(default="agent-logs")


class BigQuerySettings(BaseSettings):
    """BigQuery configuration."""
    
    model_config = SettingsConfigDict(env_prefix="BIGQUERY_")
    
    dataset: str = Field(default="kube2helm_analytics")
    table_conversions: str = Field(default="conversions")
    table_metrics: str = Field(default="metrics")
    table_errors: str = Field(default="errors")


class VertexAISettings(BaseSettings):
    """Vertex AI configuration."""
    
    model_config = SettingsConfigDict(env_prefix="VERTEX_AI_")
    
    location: str = Field(default="us-central1")
    model: str = Field(default="gemini-1.5-pro")
    endpoint: Optional[str] = Field(default=None)
    timeout: int = Field(default=120, description="Vertex AI timeout in seconds")


class RepositorySettings(BaseSettings):
    """Repository configuration."""
    
    github_token: Optional[str] = Field(default=None, alias="GITHUB_TOKEN")
    github_webhook_secret: Optional[str] = Field(default=None, alias="GITHUB_WEBHOOK_SECRET")
    gitlab_token: Optional[str] = Field(default=None, alias="GITLAB_TOKEN")
    *********************: Optional[str] = Field(default=None, alias="GITLAB_WEBHOOK_SECRET")


class KubernetesSettings(BaseSettings):
    """Kubernetes configuration."""
    
    kubeconfig: Optional[str] = Field(default=None, alias="KUBECONFIG")
    namespace: str = Field(default="kube2helm", alias="K8S_NAMESPACE")
    test_cluster_endpoint: Optional[str] = Field(default=None, alias="TEST_CLUSTER_ENDPOINT")
    test_cluster_token: Optional[str] = Field(default=None, alias="TEST_CLUSTER_TOKEN")
    timeout: int = Field(default=180, description="kubectl timeout in seconds", alias="KUBECTL_TIMEOUT")


class HelmSettings(BaseSettings):
    """Helm configuration."""
    
    model_config = SettingsConfigDict(env_prefix="HELM_")
    
    repository_url: Optional[str] = Field(default=None)
    repository_username: Optional[str] = Field(default=None)
    repository_password: Optional[str] = Field(default=None)
    timeout: int = Field(default=300, description="Helm timeout in seconds")


class MonitoringSettings(BaseSettings):
    """Monitoring and alerting configuration."""
    
    slack_webhook_url: Optional[str] = Field(default=None, alias="SLACK_WEBHOOK_URL")
    slack_channel: str = Field(default="#kube2helm-alerts", alias="SLACK_CHANNEL")
    email_smtp_host: Optional[str] = Field(default=None, alias="EMAIL_SMTP_HOST")
    email_smtp_port: int = Field(default=587, alias="EMAIL_SMTP_PORT")
    email_username: Optional[str] = Field(default=None, alias="EMAIL_USERNAME")
    email_password: Optional[str] = Field(default=None, alias="EMAIL_PASSWORD")
    alert_email_recipients: List[str] = Field(
        default_factory=list, 
        alias="ALERT_EMAIL_RECIPIENTS"
    )


class ApplicationSettings(BaseSettings):
    """Application configuration."""
    
    log_level: str = Field(default="INFO", alias="LOG_LEVEL")
    debug: bool = Field(default=False, alias="DEBUG")
    environment: str = Field(default="development", alias="ENVIRONMENT")
    api_host: str = Field(default="0.0.0.0", alias="API_HOST")
    api_port: int = Field(default=8000, alias="API_PORT")
    worker_concurrency: int = Field(default=4, alias="WORKER_CONCURRENCY")
    max_retry_attempts: int = Field(default=3, alias="MAX_RETRY_ATTEMPTS")
    retry_delay_seconds: int = Field(default=5, alias="RETRY_DELAY_SECONDS")
    http_timeout: int = Field(default=30, alias="HTTP_TIMEOUT")


class SecuritySettings(BaseSettings):
    """Security configuration."""
    
    secret_key: str = Field(..., alias="SECRET_KEY")
    jwt_secret_key: str = Field(..., alias="JWT_SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", alias="JWT_ALGORITHM")
    jwt_expiration_hours: int = Field(default=24, alias="JWT_EXPIRATION_HOURS")


class FeatureFlags(BaseSettings):
    """Feature flags configuration."""
    
    enable_auto_deployment: bool = Field(default=False, alias="ENABLE_AUTO_DEPLOYMENT")
    enable_slack_notifications: bool = Field(default=True, alias="ENABLE_SLACK_NOTIFICATIONS")
    enable_email_notifications: bool = Field(default=False, alias="ENABLE_EMAIL_NOTIFICATIONS")
    enable_bigquery_logging: bool = Field(default=True, alias="ENABLE_BIGQUERY_LOGGING")
    enable_prometheus_metrics: bool = Field(default=True, alias="ENABLE_PROMETHEUS_METRICS")


class Settings(BaseSettings):
    """Main settings class that combines all configuration sections."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    gcp: GCPSettings = Field(default_factory=GCPSettings)
    pubsub: PubSubSettings = Field(default_factory=PubSubSettings)
    storage: StorageSettings = Field(default_factory=StorageSettings)
    firestore: FirestoreSettings = Field(default_factory=FirestoreSettings)
    bigquery: BigQuerySettings = Field(default_factory=BigQuerySettings)
    vertex_ai: VertexAISettings = Field(default_factory=VertexAISettings)
    repository: RepositorySettings = Field(default_factory=RepositorySettings)
    kubernetes: KubernetesSettings = Field(default_factory=KubernetesSettings)
    helm: HelmSettings = Field(default_factory=HelmSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    application: ApplicationSettings = Field(default_factory=ApplicationSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    features: FeatureFlags = Field(default_factory=FeatureFlags)


# Global settings instance
settings = Settings()
