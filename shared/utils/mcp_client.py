"""MCP (Model Context Protocol) client wrapper for agent operations."""

import async<PERSON>
import json
import subprocess
from typing import Any, Dict, List, Optional, Union
from contextlib import asynccontextmanager

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from shared.config.mcp_servers import MCPServerConfig, mcp_config
from shared.utils.logging import get_logger

logger = get_logger(__name__)


class MCPClientManager:
    """Manager for MCP client connections and operations."""
    
    def __init__(self):
        self.active_sessions: Dict[str, ClientSession] = {}
        self.server_processes: Dict[str, subprocess.Popen] = {}
    
    @asynccontextmanager
    async def get_session(self, server_config: MCPServerConfig):
        """Get or create an MCP session for a server.
        
        Args:
            server_config: MCP server configuration
            
        Yields:
            ClientSession: Active MCP session
        """
        session_key = server_config.name
        
        if session_key in self.active_sessions:
            yield self.active_sessions[session_key]
            return
        
        try:
            # Create server parameters
            server_params = StdioServerParameters(
                command=server_config.command[0],
                args=server_config.command[1:] + server_config.args,
                env=server_config.env
            )
            
            # Create and initialize session
            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    
                    self.active_sessions[session_key] = session
                    logger.info(
                        "MCP session established",
                        server=server_config.name,
                        command=server_config.command
                    )
                    
                    try:
                        yield session
                    finally:
                        # Clean up session
                        if session_key in self.active_sessions:
                            del self.active_sessions[session_key]
                        
                        logger.info("MCP session closed", server=server_config.name)
        
        except Exception as e:
            logger.error(
                "Failed to establish MCP session",
                server=server_config.name,
                error=str(e),
                exc_info=True
            )
            raise
    
    async def call_tool(
        self,
        server_name: str,
        tool_name: str,
        arguments: Dict[str, Any]
    ) -> Any:
        """Call a tool on an MCP server.
        
        Args:
            server_name: Name of the MCP server
            tool_name: Name of the tool to call
            arguments: Tool arguments
            
        Returns:
            Tool result
        """
        server_config = getattr(mcp_config.servers, f"{server_name}_server", None)
        if not server_config:
            raise ValueError(f"Unknown MCP server: {server_name}")
        
        if not server_config.enabled:
            raise ValueError(f"MCP server disabled: {server_name}")
        
        async with self.get_session(server_config) as session:
            try:
                result = await session.call_tool(tool_name, arguments)
                
                logger.info(
                    "MCP tool called successfully",
                    server=server_name,
                    tool=tool_name,
                    arguments=arguments
                )
                
                return result
                
            except Exception as e:
                logger.error(
                    "MCP tool call failed",
                    server=server_name,
                    tool=tool_name,
                    arguments=arguments,
                    error=str(e),
                    exc_info=True
                )
                raise
    
    async def list_tools(self, server_name: str) -> List[Dict[str, Any]]:
        """List available tools on an MCP server.
        
        Args:
            server_name: Name of the MCP server
            
        Returns:
            List of available tools
        """
        server_config = getattr(mcp_config.servers, f"{server_name}_server", None)
        if not server_config:
            raise ValueError(f"Unknown MCP server: {server_name}")
        
        async with self.get_session(server_config) as session:
            tools = await session.list_tools()
            return [tool.model_dump() for tool in tools.tools]
    
    async def list_resources(self, server_name: str) -> List[Dict[str, Any]]:
        """List available resources on an MCP server.
        
        Args:
            server_name: Name of the MCP server
            
        Returns:
            List of available resources
        """
        server_config = getattr(mcp_config.servers, f"{server_name}_server", None)
        if not server_config:
            raise ValueError(f"Unknown MCP server: {server_name}")
        
        async with self.get_session(server_config) as session:
            resources = await session.list_resources()
            return [resource.model_dump() for resource in resources.resources]
    
    async def read_resource(
        self,
        server_name: str,
        resource_uri: str
    ) -> Dict[str, Any]:
        """Read a resource from an MCP server.
        
        Args:
            server_name: Name of the MCP server
            resource_uri: URI of the resource to read
            
        Returns:
            Resource content
        """
        server_config = getattr(mcp_config.servers, f"{server_name}_server", None)
        if not server_config:
            raise ValueError(f"Unknown MCP server: {server_name}")
        
        async with self.get_session(server_config) as session:
            result = await session.read_resource(resource_uri)
            return result.model_dump()
    
    async def close_all_sessions(self):
        """Close all active MCP sessions."""
        for session_key, session in list(self.active_sessions.items()):
            try:
                # Sessions are automatically closed by context managers
                pass
            except Exception as e:
                logger.error(
                    "Error closing MCP session",
                    session=session_key,
                    error=str(e)
                )
        
        self.active_sessions.clear()
        logger.info("All MCP sessions closed")


class MCPOperations:
    """High-level operations using MCP servers."""
    
    def __init__(self, client_manager: Optional[MCPClientManager] = None):
        self.client = client_manager or MCPClientManager()
    
    # Git operations
    async def git_clone(
        self,
        repository_url: str,
        target_path: str,
        branch: Optional[str] = None
    ) -> Dict[str, Any]:
        """Clone a Git repository using MCP Git server.
        
        Args:
            repository_url: Repository URL to clone
            target_path: Local path to clone to
            branch: Branch to clone (optional)
            
        Returns:
            Clone operation result
        """
        args = {
            "repository_url": repository_url,
            "target_path": target_path
        }
        if branch:
            args["branch"] = branch
        
        return await self.client.call_tool("git", "clone", args)
    
    async def git_status(self, repository_path: str) -> Dict[str, Any]:
        """Get Git repository status.
        
        Args:
            repository_path: Path to Git repository
            
        Returns:
            Repository status
        """
        return await self.client.call_tool("git", "status", {
            "repository_path": repository_path
        })
    
    # GitHub operations
    async def github_create_pr(
        self,
        owner: str,
        repo: str,
        title: str,
        body: str,
        head: str,
        base: str = "main"
    ) -> Dict[str, Any]:
        """Create a GitHub pull request.
        
        Args:
            owner: Repository owner
            repo: Repository name
            title: PR title
            body: PR body
            head: Head branch
            base: Base branch
            
        Returns:
            Created PR information
        """
        return await self.client.call_tool("github", "create_pull_request", {
            "owner": owner,
            "repo": repo,
            "title": title,
            "body": body,
            "head": head,
            "base": base
        })
    
    # Kubernetes operations
    async def kubectl_apply(
        self,
        manifest_path: str,
        namespace: Optional[str] = None
    ) -> Dict[str, Any]:
        """Apply Kubernetes manifests.
        
        Args:
            manifest_path: Path to manifest file
            namespace: Kubernetes namespace
            
        Returns:
            Apply operation result
        """
        args = {"manifest_path": manifest_path}
        if namespace:
            args["namespace"] = namespace
        
        return await self.client.call_tool("kubernetes", "apply", args)
    
    async def helm_install(
        self,
        release_name: str,
        chart_path: str,
        namespace: Optional[str] = None,
        values: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Install Helm chart.
        
        Args:
            release_name: Helm release name
            chart_path: Path to Helm chart
            namespace: Kubernetes namespace
            values: Helm values
            
        Returns:
            Install operation result
        """
        args = {
            "release_name": release_name,
            "chart_path": chart_path
        }
        if namespace:
            args["namespace"] = namespace
        if values:
            args["values"] = values
        
        return await self.client.call_tool("kubernetes", "helm_install", args)
    
    # Filesystem operations
    async def read_file(self, file_path: str) -> str:
        """Read file content using MCP filesystem server.
        
        Args:
            file_path: Path to file
            
        Returns:
            File content
        """
        result = await self.client.call_tool("filesystem", "read_file", {
            "path": file_path
        })
        return result.get("content", "")
    
    async def write_file(self, file_path: str, content: str) -> Dict[str, Any]:
        """Write file content using MCP filesystem server.
        
        Args:
            file_path: Path to file
            content: File content
            
        Returns:
            Write operation result
        """
        return await self.client.call_tool("filesystem", "write_file", {
            "path": file_path,
            "content": content
        })
    
    # Search operations
    async def search_web(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """Search the web using Brave search.
        
        Args:
            query: Search query
            max_results: Maximum number of results
            
        Returns:
            Search results
        """
        result = await self.client.call_tool("brave_search", "search", {
            "query": query,
            "count": max_results
        })
        return result.get("results", [])
    
    # Memory operations
    async def store_memory(self, key: str, value: Any) -> Dict[str, Any]:
        """Store data in memory server.
        
        Args:
            key: Memory key
            value: Value to store
            
        Returns:
            Storage result
        """
        return await self.client.call_tool("memory", "store", {
            "key": key,
            "value": json.dumps(value) if not isinstance(value, str) else value
        })
    
    async def retrieve_memory(self, key: str) -> Any:
        """Retrieve data from memory server.
        
        Args:
            key: Memory key
            
        Returns:
            Retrieved value
        """
        result = await self.client.call_tool("memory", "retrieve", {"key": key})
        value = result.get("value")
        
        # Try to parse as JSON, fallback to string
        try:
            return json.loads(value) if value else None
        except (json.JSONDecodeError, TypeError):
            return value


# Global MCP operations instance
mcp_ops = MCPOperations()
