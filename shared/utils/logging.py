"""Logging utilities for the Kube2Helm agent system."""

import logging
import sys
from typing import Any, Dict, Optional

import structlog
from google.cloud import logging as cloud_logging

from shared.config.settings import settings


def configure_logging() -> None:
    """Configure structured logging for the application."""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.application.log_level.upper())
    )
    
    # Configure Google Cloud Logging if in production
    if settings.application.environment == "production":
        try:
            client = cloud_logging.Client(project=settings.gcp.project_id)
            client.setup_logging()
        except Exception as e:
            logger = structlog.get_logger(__name__)
            logger.warning("Failed to setup Google Cloud Logging", error=str(e))


def get_logger(name: str, **kwargs: Any) -> structlog.BoundLogger:
    """Get a structured logger instance.
    
    Args:
        name: Logger name (typically __name__)
        **kwargs: Additional context to bind to the logger
        
    Returns:
        Configured structured logger
    """
    logger = structlog.get_logger(name)
    if kwargs:
        logger = logger.bind(**kwargs)
    return logger


class LogContext:
    """Context manager for adding structured logging context."""
    
    def __init__(self, logger: structlog.BoundLogger, **context: Any):
        self.logger = logger
        self.context = context
        self.original_logger = None
    
    def __enter__(self) -> structlog.BoundLogger:
        self.original_logger = self.logger
        return self.logger.bind(**self.context)
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Context is automatically removed when the bound logger goes out of scope
        pass


def log_agent_start(
    logger: structlog.BoundLogger,
    agent_type: str,
    job_id: str,
    **context: Any
) -> None:
    """Log agent start event.
    
    Args:
        logger: Structured logger
        agent_type: Type of agent starting
        job_id: Associated job ID
        **context: Additional context
    """
    logger.info(
        "Agent started",
        agent_type=agent_type,
        job_id=job_id,
        event_type="agent_start",
        **context
    )


def log_agent_complete(
    logger: structlog.BoundLogger,
    agent_type: str,
    job_id: str,
    duration_seconds: float,
    success: bool = True,
    **context: Any
) -> None:
    """Log agent completion event.
    
    Args:
        logger: Structured logger
        agent_type: Type of agent completing
        job_id: Associated job ID
        duration_seconds: Operation duration
        success: Whether the operation was successful
        **context: Additional context
    """
    logger.info(
        "Agent completed",
        agent_type=agent_type,
        job_id=job_id,
        duration_seconds=duration_seconds,
        success=success,
        event_type="agent_complete",
        **context
    )


def log_agent_error(
    logger: structlog.BoundLogger,
    agent_type: str,
    job_id: str,
    error: Exception,
    **context: Any
) -> None:
    """Log agent error event.
    
    Args:
        logger: Structured logger
        agent_type: Type of agent that errored
        job_id: Associated job ID
        error: Exception that occurred
        **context: Additional context
    """
    logger.error(
        "Agent error",
        agent_type=agent_type,
        job_id=job_id,
        error_type=type(error).__name__,
        error_message=str(error),
        event_type="agent_error",
        **context,
        exc_info=True
    )


def log_pubsub_message(
    logger: structlog.BoundLogger,
    topic: str,
    message_id: str,
    job_id: str,
    action: str = "published",
    **context: Any
) -> None:
    """Log Pub/Sub message event.
    
    Args:
        logger: Structured logger
        topic: Pub/Sub topic name
        message_id: Message ID
        job_id: Associated job ID
        action: Action performed (published, received, processed)
        **context: Additional context
    """
    logger.info(
        f"Pub/Sub message {action}",
        topic=topic,
        message_id=message_id,
        job_id=job_id,
        action=action,
        event_type="pubsub_message",
        **context
    )


def log_validation_issues(
    logger: structlog.BoundLogger,
    job_id: str,
    issues: list,
    **context: Any
) -> None:
    """Log validation issues.
    
    Args:
        logger: Structured logger
        job_id: Associated job ID
        issues: List of validation issues
        **context: Additional context
    """
    logger.warning(
        "Validation issues found",
        job_id=job_id,
        issue_count=len(issues),
        issues=issues,
        event_type="validation_issues",
        **context
    )
