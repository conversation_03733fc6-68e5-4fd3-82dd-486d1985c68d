"""Pub/Sub utilities for the Kube2Helm agent system."""

import json
from typing import Any, Callable, Dict, Optional, Type, TypeVar

from google.cloud import pubsub_v1
from google.cloud.pubsub_v1.types import PubsubMessage
from pydantic import BaseModel

from shared.config.settings import settings
from shared.models.base import BaseMessage
from shared.utils.logging import get_logger

T = TypeVar('T', bound=BaseMessage)

logger = get_logger(__name__)


class PubSubClient:
    """Pub/Sub client wrapper for the Kube2Helm system."""
    
    def __init__(self):
        self.publisher = pubsub_v1.PublisherClient()
        self.subscriber = pubsub_v1.SubscriberClient()
        self.project_id = settings.gcp.project_id
    
    def get_topic_path(self, topic_name: str) -> str:
        """Get the full topic path.
        
        Args:
            topic_name: Topic name
            
        Returns:
            Full topic path
        """
        return self.publisher.topic_path(self.project_id, topic_name)
    
    def get_subscription_path(self, subscription_name: str) -> str:
        """Get the full subscription path.
        
        Args:
            subscription_name: Subscription name
            
        Returns:
            Full subscription path
        """
        return self.subscriber.subscription_path(self.project_id, subscription_name)
    
    def publish_message(
        self, 
        topic_name: str, 
        message: BaseMessage,
        **attributes: str
    ) -> str:
        """Publish a message to a topic.
        
        Args:
            topic_name: Topic name
            message: Message to publish
            **attributes: Additional message attributes
            
        Returns:
            Message ID
        """
        topic_path = self.get_topic_path(topic_name)
        
        # Serialize message to JSON
        message_data = message.model_dump_json().encode('utf-8')
        
        # Add default attributes
        message_attributes = {
            'job_id': str(message.job_id),
            'agent_type': message.agent_type.value,
            'message_type': type(message).__name__,
            **attributes
        }
        
        # Publish message
        future = self.publisher.publish(
            topic_path, 
            message_data, 
            **message_attributes
        )
        
        message_id = future.result()
        
        logger.info(
            "Message published",
            topic=topic_name,
            message_id=message_id,
            job_id=str(message.job_id),
            message_type=type(message).__name__
        )
        
        return message_id
    
    def create_subscription(
        self, 
        topic_name: str, 
        subscription_name: str,
        ack_deadline_seconds: int = 60
    ) -> str:
        """Create a subscription for a topic.
        
        Args:
            topic_name: Topic name
            subscription_name: Subscription name
            ack_deadline_seconds: Acknowledgment deadline
            
        Returns:
            Subscription path
        """
        topic_path = self.get_topic_path(topic_name)
        subscription_path = self.get_subscription_path(subscription_name)
        
        try:
            self.subscriber.create_subscription(
                request={
                    "name": subscription_path,
                    "topic": topic_path,
                    "ack_deadline_seconds": ack_deadline_seconds
                }
            )
            logger.info(
                "Subscription created",
                topic=topic_name,
                subscription=subscription_name
            )
        except Exception as e:
            if "already exists" not in str(e).lower():
                logger.error(
                    "Failed to create subscription",
                    topic=topic_name,
                    subscription=subscription_name,
                    error=str(e)
                )
                raise
        
        return subscription_path
    
    def subscribe(
        self,
        subscription_name: str,
        callback: Callable[[PubsubMessage], None],
        max_messages: int = 100,
        flow_control_max_messages: int = 1000
    ) -> None:
        """Subscribe to messages from a subscription.
        
        Args:
            subscription_name: Subscription name
            callback: Message callback function
            max_messages: Maximum messages to pull at once
            flow_control_max_messages: Flow control max messages
        """
        subscription_path = self.get_subscription_path(subscription_name)
        
        flow_control = pubsub_v1.types.FlowControl(
            max_messages=flow_control_max_messages
        )
        
        logger.info(
            "Starting subscription",
            subscription=subscription_name
        )
        
        streaming_pull_future = self.subscriber.subscribe(
            subscription_path,
            callback=callback,
            flow_control=flow_control
        )
        
        try:
            streaming_pull_future.result(timeout=settings.pubsub.timeout)
        except KeyboardInterrupt:
            streaming_pull_future.cancel()
            logger.info("Subscription cancelled")


def parse_message(
    message: PubsubMessage, 
    message_class: Type[T]
) -> Optional[T]:
    """Parse a Pub/Sub message into a typed message object.
    
    Args:
        message: Pub/Sub message
        message_class: Expected message class
        
    Returns:
        Parsed message or None if parsing failed
    """
    try:
        message_data = json.loads(message.data.decode('utf-8'))
        return message_class.model_validate(message_data)
    except Exception as e:
        logger.error(
            "Failed to parse message",
            message_id=message.message_id,
            error=str(e),
            exc_info=True
        )
        return None


def create_message_handler(
    message_class: Type[T],
    handler_func: Callable[[T], bool]
) -> Callable[[PubsubMessage], None]:
    """Create a message handler function.
    
    Args:
        message_class: Expected message class
        handler_func: Function to handle the parsed message
        
    Returns:
        Message handler function
    """
    def handle_message(message: PubsubMessage) -> None:
        """Handle a Pub/Sub message."""
        try:
            # Parse message
            parsed_message = parse_message(message, message_class)
            if parsed_message is None:
                message.nack()
                return
            
            logger.info(
                "Processing message",
                message_id=message.message_id,
                job_id=str(parsed_message.job_id),
                message_type=type(parsed_message).__name__
            )
            
            # Handle message
            success = handler_func(parsed_message)
            
            if success:
                message.ack()
                logger.info(
                    "Message processed successfully",
                    message_id=message.message_id,
                    job_id=str(parsed_message.job_id)
                )
            else:
                message.nack()
                logger.warning(
                    "Message processing failed",
                    message_id=message.message_id,
                    job_id=str(parsed_message.job_id)
                )
                
        except Exception as e:
            logger.error(
                "Error handling message",
                message_id=message.message_id,
                error=str(e),
                exc_info=True
            )
            message.nack()
    
    return handle_message


# Global Pub/Sub client instance
pubsub_client = PubSubClient()
