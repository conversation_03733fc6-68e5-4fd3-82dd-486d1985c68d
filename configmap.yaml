apiVersion: v1
kind: ConfigMap
metadata:
  name: base-config
  labels:
    app: my-app
data:
  app.properties: |
    server.port=8080
    server.servlet.context-path=/api
    spring.datasource.driver-class-name=org.postgresql.Driver
    spring.jpa.hibernate.ddl-auto=validate
    spring.jpa.show-sql=false
    logging.level.com.mycompany=INFO
    
  logging.conf: |
    [loggers]
    keys=root,myapp
    
    [handlers]
    keys=consoleHandler,fileHandler
    
    [formatters]
    keys=simpleFormatter
    
    [logger_root]
    level=INFO
    handlers=consoleHandler
    
    [logger_myapp]
    level=DEBUG
    handlers=consoleHandler,fileHandler
    qualname=myapp
    propagate=0
    
    [handler_consoleHandler]
    class=StreamHandler
    level=INFO
    formatter=simpleFormatter
    args=(sys.stdout,)
    
    [handler_fileHandler]
    class=FileHandler
    level=DEBUG
    formatter=simpleFormatter
    args=('/app/logs/app.log',)
    
    [formatter_simpleFormatter]
    format=%(asctime)s - %(name)s - %(levelname)s - %(message)s
