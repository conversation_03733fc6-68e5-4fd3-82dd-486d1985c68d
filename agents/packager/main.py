"""Main entry point for Packager Agent."""

import asyncio
import signal
import sys
from typing import Optional

from agents.packager.agent import PackagerAgent
from agents.packager.models import PackagerConfig
from shared.config.settings import settings
from shared.models.messages import ValidatedChartMessage
from shared.utils.logging import get_logger
from shared.utils.pubsub import pubsub_client
from shared.utils.mcp_client import mcp_ops

logger = get_logger(__name__)


class PackagerAgentService:
    """Main service class for Packager Agent."""
    
    def __init__(self, config: Optional[PackagerConfig] = None):
        self.config = config or PackagerConfig()
        self.agent = PackagerAgent(self.config)
        self.running = False
        self.subscriber = None

    async def start(self):
        """Start the Packager Agent service."""
        logger.info("Starting Packager Agent service")
        
        try:
            # Validate MCP setup
            await self._validate_mcp_setup()
            
            # Setup signal handlers
            self._setup_signal_handlers()
            
            # Start message processing
            await self._start_message_processing()
            
        except Exception as e:
            logger.error("Failed to start Packager Agent service", error=str(e), exc_info=True)
            sys.exit(1)

    async def _validate_mcp_setup(self):
        """Validate MCP server connections."""
        logger.info("Validating MCP setup for Packager Agent")
        
        try:
            # Test memory server
            await mcp_ops.store_memory("packager_setup_test", "validation")
            test_value = await mcp_ops.retrieve_memory("packager_setup_test")
            if test_value != "validation":
                raise Exception("Memory server validation failed")
            
            # Test filesystem server
            try:
                test_dir = "/tmp/packager_test"
                await mcp_ops.client.call_tool("filesystem", "create_directory", {
                    "path": test_dir
                })
                await mcp_ops.client.call_tool("filesystem", "delete_directory", {
                    "path": test_dir,
                    "recursive": True
                })
                logger.info("Filesystem server validated")
            except Exception as e:
                logger.warning(f"Filesystem server validation failed: {str(e)}")
            
            # Test GitHub server if configured
            if self.config.create_pull_request:
                try:
                    github_result = await mcp_ops.client.call_tool("github", "get_user", {})
                    logger.info("GitHub server validated")
                except Exception as e:
                    logger.warning(f"GitHub server validation failed: {str(e)}")
            
            logger.info("MCP setup validation completed")
            
        except Exception as e:
            logger.error(f"MCP setup validation failed: {str(e)}")
            raise

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown")
            self.running = False
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def _start_message_processing(self):
        """Start processing messages from Pub/Sub."""
        logger.info("Starting message processing")
        self.running = True
        
        # Create subscriber
        self.subscriber = pubsub_client.create_subscriber(
            settings.pubsub.topic_validated_charts,
            self._process_message_callback
        )
        
        # Process messages
        while self.running:
            try:
                # Pull messages with timeout
                messages = await self.subscriber.pull_messages(
                    max_messages=1,
                    timeout_seconds=30
                )
                
                if not messages:
                    continue
                    
                for message in messages:
                    await self._handle_message(message)
                    
            except Exception as e:
                logger.error("Error in message processing loop", error=str(e), exc_info=True)
                await asyncio.sleep(5)  # Brief pause before retrying

    async def _handle_message(self, message):
        """Handle a single Pub/Sub message."""
        try:
            # Parse message
            validated_message = ValidatedChartMessage.model_validate_json(message.data)
            
            logger.info(
                "Processing validated charts message",
                job_id=str(validated_message.job_id),
                repository=validated_message.repository.name,
                chart_name=validated_message.helm_chart.name,
                chart_version=validated_message.helm_chart.version,
                validation_passed=validated_message.validation_result.valid,
                quality_score=validated_message.validation_result.score
            )
            
            # Process with agent
            result = await self.agent.process_message(validated_message)
            
            if result.success:
                logger.info(
                    "Successfully processed message",
                    job_id=str(validated_message.job_id),
                    duration=result.duration_seconds,
                    packages_created=result.packages_created,
                    pr_created=result.pr_created,
                    pr_url=result.pr_url,
                    registry_push=result.registry_push_success
                )
                message.ack()
            else:
                logger.error(
                    "Failed to process message",
                    job_id=str(validated_message.job_id),
                    error=result.error_message
                )
                message.nack()
                
        except Exception as e:
            logger.error("Error handling message", error=str(e), exc_info=True)
            message.nack()

    async def _process_message_callback(self, message):
        """Callback for processing Pub/Sub messages."""
        await self._handle_message(message)

    async def stop(self):
        """Stop the Packager Agent service."""
        logger.info("Stopping Packager Agent service")
        self.running = False
        
        if self.subscriber:
            await self.subscriber.close()

    async def health_check(self) -> dict:
        """Perform service health check."""
        try:
            # Test MCP connections
            await mcp_ops.store_memory("packager_health_check", "ok")
            
            return {
                "status": "healthy",
                "service": "packager-agent",
                "version": "1.0.0",
                "running": self.running,
                "mcp_servers": len(self.agent.mcp_servers),
                "configuration": {
                    "create_pull_request": self.config.create_pull_request,
                    "enable_oci_push": self.config.enable_oci_push,
                    "enable_traditional_registry": self.config.enable_traditional_registry,
                    "generate_deployment_docs": self.config.generate_deployment_docs,
                    "require_validation_pass": self.config.require_validation_pass,
                    "min_quality_score": self.config.min_quality_score
                },
                "registries": {
                    "helm_registry_url": self.config.helm_registry_url,
                    "oci_registry_url": self.config.oci_registry_url,
                    "registry_namespace": self.config.registry_namespace
                }
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "service": "packager-agent",
                "error": str(e)
            }


async def main():
    """Main entry point."""
    logger.info("Initializing Packager Agent")
    
    try:
        # Load configuration
        config = PackagerConfig()
        
        # Create and start service
        service = PackagerAgentService(config)
        await service.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down")
    except Exception as e:
        logger.error("Fatal error in main", error=str(e), exc_info=True)
        sys.exit(1)


def cli_main():
    """CLI entry point."""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error("Application failed", error=str(e), exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    cli_main()
