"""DocReference Agent implementation using MCP servers for documentation gathering."""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any
from uuid import uuid4

from agents.doc_reference.models import (
    DocReferenceConfig, DocumentationContext, DocumentationSource, 
    HelmBestPractice, DocumentationType
)
from shared.config.mcp_servers import mcp_config
from shared.models.messages import ParsedSpecMessage, ContextDocsMessage, DocReferenceResult
from shared.utils.logging import get_logger, log_agent_start, log_agent_complete, log_agent_error
from shared.utils.mcp_client import mcp_ops
from shared.utils.pubsub import pubsub_client
from shared.config.settings import settings

logger = get_logger(__name__)


class DocReferenceAgent:
    """Agent that gathers Helm best practices documentation using MCP servers."""

    def __init__(self, config: Optional[DocReferenceConfig] = None):
        self.config = config or DocReferenceConfig()
        self.mcp_servers = mcp_config.get_servers_for_agent("doc_reference")

    async def process_message(self, message: ParsedSpecMessage) -> DocReferenceResult:
        """Process parsed Kustomize message and gather documentation context.
        
        Args:
            message: Message containing parsed Kustomize structure
            
        Returns:
            DocReference agent result
        """
        job_id = message.job_id
        start_time = time.time()
        
        log_agent_start(
            logger,
            "doc_reference",
            str(job_id),
            repository_url=message.repository.url,
            resources_count=message.parsed_structure.total_resources
        )

        try:
            # Store processing state
            await mcp_ops.store_memory(f"doc_reference_job_{job_id}", {
                "status": "processing",
                "start_time": start_time,
                "parsed_structure": message.parsed_structure.model_dump()
            })

            # Gather documentation context
            documentation_context = await self._gather_documentation_with_mcp(message)
            
            if not documentation_context:
                raise Exception("Failed to gather documentation context")

            # Store context for translator agent
            await mcp_ops.store_memory(f"documentation_context_{job_id}", documentation_context.model_dump())

            # Create result
            result = DocReferenceResult(
                job_id=job_id,
                success=True,
                duration_seconds=time.time() - start_time,
                sources_found=documentation_context.total_sources,
                best_practices_count=len(documentation_context.best_practices),
                context_completeness=documentation_context.context_completeness
            )

            # Create message for translator agent
            context_message = ContextDocsMessage(
                job_id=job_id,
                repository=message.repository,
                kustomization_path=message.kustomization_path,
                parsed_structure=message.parsed_structure,
                documentation_context=documentation_context,
                webhook_payload=message.webhook_payload
            )

            # Publish to translator agent
            message_id = pubsub_client.publish_message(
                settings.pubsub.topic_helm_context,
                context_message
            )

            logger.info(
                "Published helm context message",
                job_id=str(job_id),
                message_id=message_id,
                sources_count=documentation_context.total_sources
            )

            log_agent_complete(
                logger,
                "doc_reference",
                str(job_id),
                duration_seconds=result.duration_seconds,
                sources_found=result.sources_found
            )

            return result

        except Exception as e:
            error_msg = f"DocReference agent failed: {str(e)}"
            log_agent_error(logger, "doc_reference", str(job_id), error_msg)
            
            # Store error state
            await mcp_ops.store_memory(f"doc_reference_job_{job_id}", {
                "status": "failed",
                "error": error_msg,
                "duration": time.time() - start_time
            })

            return DocReferenceResult(
                job_id=job_id,
                success=False,
                duration_seconds=time.time() - start_time,
                error_message=error_msg
            )

    async def _gather_documentation_with_mcp(self, message: ParsedSpecMessage) -> Optional[DocumentationContext]:
        """Gather documentation context using MCP web scraping and search servers.
        
        Args:
            message: Parsed Kustomize message
            
        Returns:
            Documentation context or None if gathering fails
        """
        try:
            context = DocumentationContext()
            
            # Generate search queries based on parsed structure
            search_queries = self._generate_search_queries(message.parsed_structure)
            
            logger.info(f"Generated {len(search_queries)} search queries for documentation gathering")

            # Perform searches using MCP brave search server
            search_tasks = []
            for query in search_queries[:self.config.parallel_searches]:
                task = self._search_documentation(query)
                search_tasks.append(task)
            
            # Execute searches in parallel
            search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
            
            # Process search results
            for result in search_results:
                if isinstance(result, Exception):
                    logger.warning(f"Search failed: {str(result)}")
                    continue
                    
                if result:
                    context.sources.extend(result)

            # Extract content from sources using MCP web server
            await self._extract_content_from_sources(context.sources)
            
            # Process documentation into best practices
            context.best_practices = await self._extract_best_practices(context.sources)
            
            # Generate resource-specific guidance
            context.resource_guidance = self._generate_resource_guidance(
                message.parsed_structure, context.best_practices
            )
            
            # Extract common patterns and guidelines
            context.common_patterns = self._extract_common_patterns(context.sources)
            context.security_guidelines = self._extract_security_guidelines(context.sources)
            context.performance_tips = self._extract_performance_tips(context.sources)
            
            # Calculate metrics
            context.total_sources = len(context.sources)
            context.average_relevance = self._calculate_average_relevance(context.sources)
            context.context_completeness = self._calculate_completeness_score(context)

            logger.info(
                f"Successfully gathered documentation context",
                total_sources=context.total_sources,
                best_practices=len(context.best_practices),
                completeness=context.context_completeness
            )

            return context

        except Exception as e:
            logger.error(f"Failed to gather documentation context: {str(e)}", exc_info=True)
            return None

    def _generate_search_queries(self, parsed_structure) -> List[str]:
        """Generate targeted search queries based on parsed Kustomize structure."""
        queries = list(self.config.base_search_queries)
        
        # Add resource-specific queries
        for resource_type, count in parsed_structure.resource_types.items():
            if count > 0:
                queries.extend([
                    f"helm chart {resource_type.lower()} best practices",
                    f"kubernetes {resource_type.lower()} to helm conversion",
                    f"helm {resource_type.lower()} template examples"
                ])
        
        # Add complexity-based queries
        if parsed_structure.complexity_score > 50:
            queries.extend([
                "helm chart complex application patterns",
                "helm chart microservices best practices",
                "helm chart dependency management"
            ])
        
        # Add namespace-specific queries if namespace is used
        if parsed_structure.namespace:
            queries.append("helm chart namespace best practices")
            
        # Add configuration-specific queries
        if parsed_structure.config_map_generator:
            queries.append("helm configmap generation best practices")
            
        if parsed_structure.secret_generator:
            queries.append("helm secret management best practices")
            
        return list(set(queries))  # Remove duplicates

    async def _search_documentation(self, query: str) -> List[DocumentationSource]:
        """Search for documentation using MCP brave search server."""
        try:
            logger.info(f"Searching documentation for: {query}")

            # Use MCP brave search server
            search_results = await mcp_ops.client.call_tool("brave_search", "search", {
                "query": query,
                "count": self.config.max_search_results // len(self.config.base_search_queries)
            })

            sources = []
            if search_results and "results" in search_results:
                for result in search_results["results"]:
                    source = DocumentationSource(
                        url=result.get("url", ""),
                        title=result.get("title", ""),
                        content="",  # Will be filled by content extraction
                        doc_type=self._classify_documentation_type(result.get("url", "")),
                        relevance_score=self._calculate_relevance_score(result, query)
                    )

                    if source.relevance_score >= self.config.min_relevance_score:
                        sources.append(source)

            logger.info(f"Found {len(sources)} relevant sources for query: {query}")
            return sources

        except Exception as e:
            logger.error(f"Search failed for query '{query}': {str(e)}")
            return []

    async def _extract_content_from_sources(self, sources: List[DocumentationSource]):
        """Extract content from documentation sources using MCP web server."""
        for source in sources:
            try:
                # Use MCP puppeteer server for web scraping
                content_result = await mcp_ops.client.call_tool("web", "scrape", {
                    "url": source.url,
                    "selector": "body",
                    "extract_text": True
                })

                if content_result and "content" in content_result:
                    # Limit content length
                    content = content_result["content"][:self.config.max_content_length]
                    source.content = content

                    # Extract metadata if available
                    if "last_modified" in content_result:
                        source.last_updated = content_result["last_modified"]

            except Exception as e:
                logger.warning(f"Failed to extract content from {source.url}: {str(e)}")
                source.content = ""

    async def _extract_best_practices(self, sources: List[DocumentationSource]) -> List[HelmBestPractice]:
        """Extract best practices from documentation sources using AI."""
        best_practices = []

        try:
            # Combine all source content
            combined_content = "\n\n".join([
                f"Source: {source.title}\nURL: {source.url}\nContent: {source.content}"
                for source in sources if source.content
            ])

            if not combined_content:
                return best_practices

            # Use MCP sequential thinking server for structured analysis
            analysis_prompt = f"""
            Analyze the following Helm documentation and extract specific best practices.

            For each best practice, provide:
            1. Category (e.g., "Security", "Performance", "Structure", "Values")
            2. Title (brief description)
            3. Description (detailed explanation)
            4. Example (code snippet if available)
            5. Rationale (why it's important)
            6. Applies to (resource types)
            7. Priority (low/medium/high/critical)

            Documentation content:
            {combined_content[:10000]}  # Limit content for processing

            Extract the most important and actionable best practices.
            """

            analysis_result = await mcp_ops.client.call_tool("sequential_thinking", "analyze", {
                "prompt": analysis_prompt,
                "format": "structured"
            })

            if analysis_result and "practices" in analysis_result:
                for practice_data in analysis_result["practices"]:
                    practice = HelmBestPractice(
                        category=practice_data.get("category", "General"),
                        title=practice_data.get("title", ""),
                        description=practice_data.get("description", ""),
                        example=practice_data.get("example"),
                        rationale=practice_data.get("rationale", ""),
                        applies_to=practice_data.get("applies_to", []),
                        priority=practice_data.get("priority", "medium"),
                        source_url=practice_data.get("source_url")
                    )
                    best_practices.append(practice)

        except Exception as e:
            logger.error(f"Failed to extract best practices: {str(e)}")

        return best_practices

    def _classify_documentation_type(self, url: str) -> DocumentationType:
        """Classify documentation type based on URL."""
        url_lower = url.lower()

        if "helm.sh" in url_lower:
            return DocumentationType.HELM_OFFICIAL
        elif "kubernetes.io" in url_lower:
            return DocumentationType.KUBERNETES_OFFICIAL
        elif "github.com" in url_lower:
            return DocumentationType.GITHUB_ISSUE
        elif "stackoverflow.com" in url_lower:
            return DocumentationType.STACKOVERFLOW
        elif any(domain in url_lower for domain in ["medium.com", "dev.to", "blog"]):
            return DocumentationType.BLOG_POST
        elif "tutorial" in url_lower:
            return DocumentationType.TUTORIAL
        else:
            return DocumentationType.REFERENCE

    def _calculate_relevance_score(self, result: Dict[str, Any], query: str) -> float:
        """Calculate relevance score for search result."""
        score = 0.0

        title = result.get("title", "").lower()
        description = result.get("description", "").lower()
        url = result.get("url", "").lower()

        query_terms = query.lower().split()

        # Score based on title matches
        for term in query_terms:
            if term in title:
                score += 0.3
            if term in description:
                score += 0.2
            if term in url:
                score += 0.1

        # Bonus for official sources
        if any(domain in url for domain in self.config.official_sources):
            score += 0.2

        # Normalize to 0-1 range
        return min(score, 1.0)

    def _generate_resource_guidance(self, parsed_structure, best_practices: List[HelmBestPractice]) -> Dict[str, List[str]]:
        """Generate resource-specific guidance."""
        guidance = {}

        for resource_type in parsed_structure.resource_types.keys():
            resource_guidance = []

            # Find applicable best practices
            for practice in best_practices:
                if resource_type.lower() in [applies.lower() for applies in practice.applies_to]:
                    resource_guidance.append(f"{practice.title}: {practice.description}")

            # Add general guidance based on resource type
            if resource_type == "Deployment":
                resource_guidance.extend([
                    "Use resource limits and requests",
                    "Configure readiness and liveness probes",
                    "Set appropriate replica count"
                ])
            elif resource_type == "Service":
                resource_guidance.extend([
                    "Choose appropriate service type",
                    "Configure proper port mappings",
                    "Consider service mesh integration"
                ])
            elif resource_type == "ConfigMap":
                resource_guidance.extend([
                    "Use values.yaml for configuration",
                    "Avoid hardcoded values in templates",
                    "Consider configuration validation"
                ])

            if resource_guidance:
                guidance[resource_type] = resource_guidance

        return guidance

    def _extract_common_patterns(self, sources: List[DocumentationSource]) -> List[Dict[str, Any]]:
        """Extract common Helm patterns from documentation."""
        patterns = [
            {
                "name": "Values Structure",
                "description": "Organize values.yaml with clear hierarchy",
                "example": "image:\n  repository: nginx\n  tag: latest\n  pullPolicy: IfNotPresent"
            },
            {
                "name": "Template Helpers",
                "description": "Use _helpers.tpl for reusable template functions",
                "example": "{{- define \"myapp.name\" -}}\n{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix \"-\" -}}\n{{- end -}}"
            },
            {
                "name": "Resource Labels",
                "description": "Apply consistent labeling strategy",
                "example": "labels:\n  app.kubernetes.io/name: {{ include \"myapp.name\" . }}\n  app.kubernetes.io/version: {{ .Chart.AppVersion }}"
            }
        ]
        return patterns

    def _extract_security_guidelines(self, sources: List[DocumentationSource]) -> List[str]:
        """Extract security guidelines from documentation."""
        return [
            "Use non-root containers when possible",
            "Set resource limits to prevent resource exhaustion",
            "Use secrets for sensitive data, not ConfigMaps",
            "Enable Pod Security Standards",
            "Validate input values in templates",
            "Use least privilege RBAC permissions",
            "Scan container images for vulnerabilities"
        ]

    def _extract_performance_tips(self, sources: List[DocumentationSource]) -> List[str]:
        """Extract performance optimization tips."""
        return [
            "Set appropriate resource requests and limits",
            "Use horizontal pod autoscaling for variable workloads",
            "Configure readiness probes to avoid traffic to unready pods",
            "Use persistent volumes for stateful applications",
            "Optimize container image size",
            "Configure proper DNS settings",
            "Use node affinity for workload placement"
        ]

    def _calculate_average_relevance(self, sources: List[DocumentationSource]) -> float:
        """Calculate average relevance score of sources."""
        if not sources:
            return 0.0
        return sum(source.relevance_score for source in sources) / len(sources)

    def _calculate_completeness_score(self, context: DocumentationContext) -> float:
        """Calculate completeness score of documentation context."""
        score = 0.0

        # Base score from number of sources
        if context.total_sources >= 10:
            score += 0.3
        elif context.total_sources >= 5:
            score += 0.2
        elif context.total_sources >= 1:
            score += 0.1

        # Score from best practices
        if len(context.best_practices) >= 10:
            score += 0.3
        elif len(context.best_practices) >= 5:
            score += 0.2
        elif len(context.best_practices) >= 1:
            score += 0.1

        # Score from resource guidance
        if len(context.resource_guidance) >= 3:
            score += 0.2
        elif len(context.resource_guidance) >= 1:
            score += 0.1

        # Score from patterns and guidelines
        if context.common_patterns:
            score += 0.1
        if context.security_guidelines:
            score += 0.1
        if context.performance_tips:
            score += 0.1

        return min(score, 1.0)
