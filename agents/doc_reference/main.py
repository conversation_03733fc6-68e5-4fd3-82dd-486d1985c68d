"""Main entry point for DocReference Agent."""

import asyncio
import signal
import sys
from typing import Optional

from agents.doc_reference.agent import DocReferenceAgent
from agents.doc_reference.models import DocReferenceConfig
from shared.config.settings import settings
from shared.models.messages import ParsedKustomizeMessage
from shared.utils.logging import get_logger
from shared.utils.pubsub import pubsub_client
from shared.utils.mcp_client import mcp_ops

logger = get_logger(__name__)


class DocReferenceAgentService:
    """Main service class for DocReference Agent."""
    
    def __init__(self, config: Optional[DocReferenceConfig] = None):
        self.config = config or DocReferenceConfig()
        self.agent = DocReferenceAgent(self.config)
        self.running = False
        self.subscriber = None

    async def start(self):
        """Start the DocReference Agent service."""
        logger.info("Starting DocReference Agent service")
        
        try:
            # Validate MCP setup
            await self._validate_mcp_setup()
            
            # Setup signal handlers
            self._setup_signal_handlers()
            
            # Start message processing
            await self._start_message_processing()
            
        except Exception as e:
            logger.error("Failed to start DocReference Agent service", error=str(e), exc_info=True)
            sys.exit(1)

    async def _validate_mcp_setup(self):
        """Validate MCP server connections."""
        logger.info("Validating MCP setup for DocReference Agent")
        
        try:
            # Test memory server
            await mcp_ops.store_memory("doc_reference_setup_test", "validation")
            test_value = await mcp_ops.retrieve_memory("doc_reference_setup_test")
            if test_value != "validation":
                raise Exception("Memory server validation failed")
            
            # Test brave search server
            try:
                search_result = await mcp_ops.client.call_tool("brave_search", "search", {
                    "query": "helm best practices test",
                    "count": 1
                })
                logger.info("Brave search server validated")
            except Exception as e:
                logger.warning(f"Brave search server validation failed: {str(e)}")
            
            # Test web scraping server
            try:
                tools = await mcp_ops.client.list_tools("web")
                logger.info(f"Web server tools available: {len(tools)}")
            except Exception as e:
                logger.warning(f"Web server validation failed: {str(e)}")
            
            logger.info("MCP setup validation completed")
            
        except Exception as e:
            logger.error(f"MCP setup validation failed: {str(e)}")
            raise

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown")
            self.running = False
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def _start_message_processing(self):
        """Start processing messages from Pub/Sub."""
        logger.info("Starting message processing")
        self.running = True
        
        # Create subscriber
        self.subscriber = pubsub_client.create_subscriber(
            settings.pubsub.topic_parsed_kustomize,
            self._process_message_callback
        )
        
        # Process messages
        while self.running:
            try:
                # Pull messages with timeout
                messages = await self.subscriber.pull_messages(
                    max_messages=1,
                    timeout_seconds=30
                )
                
                if not messages:
                    continue
                    
                for message in messages:
                    await self._handle_message(message)
                    
            except Exception as e:
                logger.error("Error in message processing loop", error=str(e), exc_info=True)
                await asyncio.sleep(5)  # Brief pause before retrying

    async def _handle_message(self, message):
        """Handle a single Pub/Sub message."""
        try:
            # Parse message
            parsed_message = ParsedKustomizeMessage.model_validate_json(message.data)
            
            logger.info(
                "Processing parsed kustomize message",
                job_id=str(parsed_message.job_id),
                repository=parsed_message.repository.name,
                resources_count=parsed_message.parsed_structure.total_resources
            )
            
            # Process with agent
            result = await self.agent.process_message(parsed_message)
            
            if result.success:
                logger.info(
                    "Successfully processed message",
                    job_id=str(parsed_message.job_id),
                    duration=result.duration_seconds,
                    sources_found=result.sources_found,
                    best_practices=result.best_practices_count
                )
                message.ack()
            else:
                logger.error(
                    "Failed to process message",
                    job_id=str(parsed_message.job_id),
                    error=result.error_message
                )
                message.nack()
                
        except Exception as e:
            logger.error("Error handling message", error=str(e), exc_info=True)
            message.nack()

    async def _process_message_callback(self, message):
        """Callback for processing Pub/Sub messages."""
        await self._handle_message(message)

    async def stop(self):
        """Stop the DocReference Agent service."""
        logger.info("Stopping DocReference Agent service")
        self.running = False
        
        if self.subscriber:
            await self.subscriber.close()

    async def health_check(self) -> dict:
        """Perform service health check."""
        try:
            # Test MCP connections
            await mcp_ops.store_memory("doc_reference_health_check", "ok")
            
            return {
                "status": "healthy",
                "service": "doc-reference-agent",
                "version": "1.0.0",
                "running": self.running,
                "mcp_servers": len(self.agent.mcp_servers)
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "service": "doc-reference-agent",
                "error": str(e)
            }


async def main():
    """Main entry point."""
    logger.info("Initializing DocReference Agent")
    
    try:
        # Load configuration
        config = DocReferenceConfig()
        
        # Create and start service
        service = DocReferenceAgentService(config)
        await service.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down")
    except Exception as e:
        logger.error("Fatal error in main", error=str(e), exc_info=True)
        sys.exit(1)


def cli_main():
    """CLI entry point."""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error("Application failed", error=str(e), exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    cli_main()
