"""Data models for DocReference Agent."""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class DocumentationType(str, Enum):
    """Types of documentation sources."""
    HELM_OFFICIAL = "helm_official"
    KUBERNETES_OFFICIAL = "kubernetes_official"
    BEST_PRACTICES = "best_practices"
    COMMUNITY_GUIDE = "community_guide"
    BLOG_POST = "blog_post"
    STACKOVERFLOW = "stackoverflow"
    GITHUB_ISSUE = "github_issue"
    TUTORIAL = "tutorial"
    REFERENCE = "reference"


class DocumentationSource(BaseModel):
    """Represents a documentation source."""
    
    url: str = Field(..., description="Source URL")
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Extracted content")
    doc_type: DocumentationType = Field(..., description="Type of documentation")
    relevance_score: float = Field(default=0.0, description="Relevance score (0-1)")
    last_updated: Optional[str] = Field(default=None, description="Last updated date")
    author: Optional[str] = Field(default=None, description="Author or organization")
    tags: List[str] = Field(default_factory=list, description="Content tags")


class HelmBestPractice(BaseModel):
    """Represents a specific Helm best practice."""
    
    category: str = Field(..., description="Best practice category")
    title: str = Field(..., description="Practice title")
    description: str = Field(..., description="Detailed description")
    example: Optional[str] = Field(default=None, description="Code example")
    rationale: str = Field(..., description="Why this practice is important")
    applies_to: List[str] = Field(default_factory=list, description="Resource types this applies to")
    priority: str = Field(default="medium", description="Priority level (low/medium/high/critical)")
    source_url: Optional[str] = Field(default=None, description="Source documentation URL")


class DocumentationContext(BaseModel):
    """Complete documentation context for Helm conversion."""
    
    # Source documentation
    sources: List[DocumentationSource] = Field(default_factory=list, description="All documentation sources")
    
    # Extracted best practices
    best_practices: List[HelmBestPractice] = Field(default_factory=list, description="Relevant best practices")
    
    # Resource-specific guidance
    resource_guidance: Dict[str, List[str]] = Field(default_factory=dict, description="Guidance by resource type")
    
    # Common patterns
    common_patterns: List[Dict[str, Any]] = Field(default_factory=list, description="Common Helm patterns")
    
    # Security considerations
    security_guidelines: List[str] = Field(default_factory=list, description="Security best practices")
    
    # Performance recommendations
    performance_tips: List[str] = Field(default_factory=list, description="Performance optimization tips")
    
    # Metadata
    total_sources: int = Field(default=0, description="Total number of sources")
    average_relevance: float = Field(default=0.0, description="Average relevance score")
    context_completeness: float = Field(default=0.0, description="Completeness score (0-1)")


class DocReferenceConfig(BaseModel):
    """Configuration for DocReference Agent."""
    
    # Search configuration
    max_search_results: int = Field(default=20, description="Maximum search results per query")
    min_relevance_score: float = Field(default=0.3, description="Minimum relevance score to include")
    search_timeout_seconds: int = Field(default=60, description="Search timeout per query")
    
    # Content extraction
    max_content_length: int = Field(default=5000, description="Maximum content length per source")
    extract_code_examples: bool = Field(default=True, description="Extract code examples")
    extract_yaml_snippets: bool = Field(default=True, description="Extract YAML snippets")
    
    # Documentation sources
    official_sources: List[str] = Field(
        default_factory=lambda: [
            "helm.sh",
            "kubernetes.io",
            "github.com/helm/helm",
            "github.com/helm/charts"
        ],
        description="Official documentation sources"
    )
    
    community_sources: List[str] = Field(
        default_factory=lambda: [
            "medium.com",
            "dev.to",
            "stackoverflow.com",
            "reddit.com/r/kubernetes"
        ],
        description="Community documentation sources"
    )
    
    # Search queries
    base_search_queries: List[str] = Field(
        default_factory=lambda: [
            "helm chart best practices",
            "kubernetes to helm migration",
            "helm chart structure",
            "helm values.yaml best practices",
            "helm chart security",
            "helm chart performance"
        ],
        description="Base search queries"
    )
    
    # Processing
    enable_ai_summarization: bool = Field(default=True, description="Use AI to summarize content")
    cache_results: bool = Field(default=True, description="Cache search results")
    cache_ttl_hours: int = Field(default=24, description="Cache TTL in hours")
    parallel_searches: int = Field(default=3, description="Number of parallel searches")
    
    # Quality filters
    min_content_quality_score: float = Field(default=0.5, description="Minimum content quality score")
    exclude_outdated_content: bool = Field(default=True, description="Exclude outdated content")
    max_content_age_days: int = Field(default=365, description="Maximum content age in days")
