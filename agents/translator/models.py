"""Data models for Translator Agent."""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class HelmTemplateType(str, Enum):
    """Types of Helm templates."""
    DEPLOYMENT = "deployment"
    SERVICE = "service"
    CONFIGMAP = "configmap"
    SECRET = "secret"
    INGRESS = "ingress"
    PVC = "persistentvolumeclaim"
    SERVICE_ACCOUNT = "serviceaccount"
    RBAC = "rbac"
    HPA = "horizontalpodautoscaler"
    NETWORK_POLICY = "networkpolicy"
    CUSTOM = "custom"


class HelmTemplate(BaseModel):
    """Represents a generated Helm template."""
    
    name: str = Field(..., description="Template file name")
    template_type: HelmTemplateType = Field(..., description="Type of template")
    content: str = Field(..., description="Template YAML content")
    values_schema: Dict[str, Any] = Field(default_factory=dict, description="Values schema for this template")
    notes: List[str] = Field(default_factory=list, description="Generation notes and comments")
    source_resources: List[str] = Field(default_factory=list, description="Source Kustomize resources")


class HelmValues(BaseModel):
    """Represents Helm values.yaml structure."""
    
    global_values: Dict[str, Any] = Field(default_factory=dict, description="Global values")
    application_values: Dict[str, Any] = Field(default_factory=dict, description="Application-specific values")
    environment_values: Dict[str, Any] = Field(default_factory=dict, description="Environment-specific values")
    schema: Dict[str, Any] = Field(default_factory=dict, description="Values schema definition")


class HelmChart(BaseModel):
    """Represents a complete Helm chart."""
    
    # Chart metadata
    name: str = Field(..., description="Chart name")
    version: str = Field(default="0.1.0", description="Chart version")
    app_version: str = Field(default="1.0.0", description="Application version")
    description: str = Field(..., description="Chart description")
    keywords: List[str] = Field(default_factory=list, description="Chart keywords")
    
    # Chart files
    templates: List[HelmTemplate] = Field(default_factory=list, description="Helm templates")
    values: HelmValues = Field(default_factory=HelmValues, description="Values configuration")
    
    # Chart structure
    dependencies: List[Dict[str, Any]] = Field(default_factory=list, description="Chart dependencies")
    helpers: str = Field(default="", description="_helpers.tpl content")
    notes: str = Field(default="", description="NOTES.txt content")
    
    # Metadata
    total_templates: int = Field(default=0, description="Total number of templates")
    complexity_score: float = Field(default=0.0, description="Chart complexity score")
    conversion_notes: List[str] = Field(default_factory=list, description="Conversion notes")


class TranslationStrategy(BaseModel):
    """Strategy for Kustomize to Helm translation."""
    
    # Template generation
    preserve_structure: bool = Field(default=True, description="Preserve original resource structure")
    generate_helpers: bool = Field(default=True, description="Generate helper templates")
    use_subchart_pattern: bool = Field(default=False, description="Use subchart pattern for complex apps")
    
    # Values extraction
    extract_common_values: bool = Field(default=True, description="Extract common values")
    create_environment_values: bool = Field(default=True, description="Create environment-specific values")
    generate_values_schema: bool = Field(default=True, description="Generate values schema")
    
    # Best practices
    apply_security_practices: bool = Field(default=True, description="Apply security best practices")
    optimize_for_performance: bool = Field(default=True, description="Apply performance optimizations")
    add_monitoring_labels: bool = Field(default=True, description="Add monitoring and observability labels")
    
    # Customization
    custom_labels: Dict[str, str] = Field(default_factory=dict, description="Custom labels to add")
    custom_annotations: Dict[str, str] = Field(default_factory=dict, description="Custom annotations to add")
    namespace_override: Optional[str] = Field(default=None, description="Override namespace")


class TranslatorConfig(BaseModel):
    """Configuration for Translator Agent."""
    
    # AI/LLM configuration
    model_name: str = Field(default="gemini-1.5-pro", description="Vertex AI model name")
    temperature: float = Field(default=0.1, description="LLM temperature for consistency")
    max_tokens: int = Field(default=8192, description="Maximum tokens per request")
    
    # Translation settings
    translation_strategy: TranslationStrategy = Field(default_factory=TranslationStrategy)
    max_resources_per_template: int = Field(default=1, description="Maximum resources per template file")
    template_naming_convention: str = Field(default="kebab-case", description="Template naming convention")
    
    # Quality control
    validate_generated_yaml: bool = Field(default=True, description="Validate generated YAML syntax")
    apply_best_practices: bool = Field(default=True, description="Apply Helm best practices")
    generate_documentation: bool = Field(default=True, description="Generate chart documentation")
    
    # Processing limits
    max_processing_time_seconds: int = Field(default=600, description="Maximum processing time")
    max_chart_complexity: float = Field(default=100.0, description="Maximum allowed chart complexity")
    retry_attempts: int = Field(default=3, description="Number of retry attempts for failed conversions")
    
    # Output configuration
    include_conversion_comments: bool = Field(default=True, description="Include conversion comments in templates")
    generate_upgrade_notes: bool = Field(default=True, description="Generate upgrade notes")
    create_example_values: bool = Field(default=True, description="Create example values files")
