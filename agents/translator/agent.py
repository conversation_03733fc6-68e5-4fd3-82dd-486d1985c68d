"""Translator Agent implementation using Vertex AI and MCP servers."""

import asyncio
import json
import time
import yaml
from typing import Dict, List, Optional, Any
from uuid import uuid4

from agents.translator.models import (
    TranslatorConfig, <PERSON><PERSON><PERSON><PERSON>, He<PERSON><PERSON><PERSON>plate, <PERSON><PERSON><PERSON><PERSON><PERSON>, 
    HelmT<PERSON>plateType, TranslationStrategy
)
from shared.config.mcp_servers import mcp_config
from shared.models.messages import ContextDocsMessage, DraftChartMessage, TranslatorResult
from shared.utils.logging import get_logger, log_agent_start, log_agent_complete, log_agent_error
from shared.utils.mcp_client import mcp_ops
from shared.utils.pubsub import pubsub_client
from shared.config.settings import settings

logger = get_logger(__name__)


class TranslatorAgent:
    """Agent that converts Kustomize to Helm using Vertex AI and MCP servers."""

    def __init__(self, config: Optional[TranslatorConfig] = None):
        self.config = config or TranslatorConfig()
        self.mcp_servers = mcp_config.get_servers_for_agent("translator")

    async def process_message(self, message: ContextDocsMessage) -> TranslatorResult:
        """Process helm context message and convert to Helm charts.

        Args:
            message: Message containing parsed Kustomize and documentation context

        Returns:
            Translator agent result
        """
        job_id = message.job_id
        start_time = time.time()
        
        log_agent_start(
            logger,
            "translator",
            str(job_id),
            repository_url=message.repository.url,
            resources_count=message.parsed_structure.total_resources
        )

        try:
            # Store processing state
            await mcp_ops.store_memory(f"translator_job_{job_id}", {
                "status": "processing",
                "start_time": start_time,
                "parsed_structure": message.parsed_structure.model_dump(),
                "documentation_context": message.documentation_context.model_dump()
            })

            # Convert Kustomize to Helm using AI
            helm_chart = await self._convert_with_vertex_ai(message)
            
            if not helm_chart:
                raise Exception("Failed to convert Kustomize to Helm chart")

            # Store chart for linter agent
            await mcp_ops.store_memory(f"helm_chart_{job_id}", helm_chart.model_dump())

            # Create result
            result = TranslatorResult(
                job_id=job_id,
                success=True,
                duration_seconds=time.time() - start_time,
                templates_generated=helm_chart.total_templates,
                chart_complexity=helm_chart.complexity_score
            )

            # Create message for linter agent
            charts_message = DraftChartMessage(
                job_id=job_id,
                repository=message.repository,
                kustomization_path=message.kustomization_path,
                parsed_structure=message.parsed_structure,
                documentation_context=message.documentation_context,
                helm_chart=helm_chart,
                webhook_payload=message.webhook_payload
            )

            # Publish to linter agent
            message_id = pubsub_client.publish_message(
                settings.pubsub.topic_helm_charts,
                charts_message
            )

            logger.info(
                "Published helm charts message",
                job_id=str(job_id),
                message_id=message_id,
                templates_count=helm_chart.total_templates
            )

            log_agent_complete(
                logger,
                "translator",
                str(job_id),
                duration_seconds=result.duration_seconds,
                templates_generated=result.templates_generated
            )

            return result

        except Exception as e:
            error_msg = f"Translator agent failed: {str(e)}"
            log_agent_error(logger, "translator", str(job_id), error_msg)
            
            # Store error state
            await mcp_ops.store_memory(f"translator_job_{job_id}", {
                "status": "failed",
                "error": error_msg,
                "duration": time.time() - start_time
            })

            return TranslatorResult(
                job_id=job_id,
                success=False,
                duration_seconds=time.time() - start_time,
                error_message=error_msg
            )

    async def _convert_with_vertex_ai(self, message: ContextDocsMessage) -> Optional[HelmChart]:
        """Convert Kustomize to Helm using Vertex AI and MCP sequential thinking.

        Args:
            message: Helm context message with all conversion data

        Returns:
            Generated Helm chart or None if conversion fails
        """
        try:
            # Prepare conversion context
            conversion_context = self._prepare_conversion_context(message)
            
            logger.info(f"Starting AI-powered conversion for job {message.job_id}")

            # Use MCP sequential thinking server for structured conversion
            conversion_prompt = self._build_conversion_prompt(conversion_context)
            
            # Execute conversion using sequential thinking
            conversion_result = await mcp_ops.client.call_tool("sequential_thinking", "process", {
                "prompt": conversion_prompt,
                "steps": [
                    "analyze_kustomize_structure",
                    "extract_values_candidates", 
                    "generate_helm_templates",
                    "create_values_yaml",
                    "apply_best_practices",
                    "validate_output"
                ],
                "format": "structured_helm_chart"
            })
            
            if not conversion_result or "helm_chart" not in conversion_result:
                raise Exception("AI conversion did not produce valid Helm chart")

            # Parse AI response into HelmChart model
            helm_chart = self._parse_ai_response(conversion_result["helm_chart"], message)
            
            # Apply post-processing improvements
            await self._apply_post_processing(helm_chart, message)
            
            logger.info(
                f"Successfully converted Kustomize to Helm",
                templates_count=helm_chart.total_templates,
                complexity_score=helm_chart.complexity_score
            )

            return helm_chart

        except Exception as e:
            logger.error(f"Failed to convert with Vertex AI: {str(e)}", exc_info=True)
            return None

    def _prepare_conversion_context(self, message: ContextDocsMessage) -> Dict[str, Any]:
        """Prepare comprehensive context for AI conversion."""
        return {
            "kustomize_structure": {
                "resources": [resource.model_dump() for resource in message.parsed_structure.resources],
                "namespace": message.parsed_structure.namespace,
                "name_prefix": message.parsed_structure.name_prefix,
                "name_suffix": message.parsed_structure.name_suffix,
                "common_labels": message.parsed_structure.common_labels,
                "common_annotations": message.parsed_structure.common_annotations,
                "images": message.parsed_structure.images,
                "config_map_generator": message.parsed_structure.config_map_generator,
                "secret_generator": message.parsed_structure.secret_generator,
                "complexity_score": message.parsed_structure.complexity_score
            },
            "documentation_context": {
                "best_practices": [practice.model_dump() for practice in message.documentation_context.best_practices],
                "resource_guidance": message.documentation_context.resource_guidance,
                "common_patterns": message.documentation_context.common_patterns,
                "security_guidelines": message.documentation_context.security_guidelines,
                "performance_tips": message.documentation_context.performance_tips
            },
            "translation_strategy": self.config.translation_strategy.model_dump(),
            "repository_info": {
                "name": message.repository.name,
                "url": message.repository.url
            }
        }

    def _build_conversion_prompt(self, context: Dict[str, Any]) -> str:
        """Build comprehensive prompt for AI conversion."""
        return f"""
You are an expert Kubernetes and Helm engineer tasked with converting a Kustomize configuration to a production-ready Helm chart.

## Input Kustomize Configuration:
{json.dumps(context["kustomize_structure"], indent=2)}

## Available Best Practices and Documentation:
{json.dumps(context["documentation_context"], indent=2)}

## Translation Strategy:
{json.dumps(context["translation_strategy"], indent=2)}

## Your Task:
Convert the Kustomize configuration to a complete Helm chart following these requirements:

### 1. Analyze Kustomize Structure
- Identify all Kubernetes resources and their relationships
- Understand the application architecture and dependencies
- Note any complex configurations or custom resources

### 2. Extract Values Candidates
- Identify hardcoded values that should be parameterized
- Group related configuration values logically
- Consider environment-specific vs application-specific values
- Create a hierarchical values structure

### 3. Generate Helm Templates
- Create separate template files for each resource type
- Use Helm templating best practices (helpers, conditionals, loops)
- Apply consistent naming conventions
- Include proper labels and annotations
- Add resource limits and security contexts where missing

### 4. Create Values.yaml
- Structure values hierarchically and logically
- Provide sensible defaults
- Include comments explaining each value
- Separate global, application, and environment values

### 5. Apply Best Practices
- Implement security best practices from the documentation
- Add performance optimizations
- Include monitoring and observability labels
- Follow Helm chart conventions
- Add proper RBAC if needed

### 6. Validate Output
- Ensure all templates are valid YAML
- Check that all values are properly referenced
- Verify resource relationships are maintained
- Confirm chart follows Helm standards

## Output Format:
Provide a complete Helm chart structure with:
- Chart.yaml metadata
- All template files with content
- values.yaml with full structure
- _helpers.tpl with reusable functions
- NOTES.txt with usage instructions
- Conversion notes and recommendations

Focus on creating a production-ready, maintainable, and secure Helm chart that follows industry best practices.
"""

    def _parse_ai_response(self, ai_response: Dict[str, Any], message: ContextDocsMessage) -> HelmChart:
        """Parse AI response into HelmChart model."""
        try:
            # Extract chart metadata
            chart_name = ai_response.get("name", message.repository.name.split("/")[-1])
            chart_description = ai_response.get("description", f"Helm chart for {chart_name}")

            # Create HelmChart instance
            helm_chart = HelmChart(
                name=chart_name,
                description=chart_description,
                version=ai_response.get("version", "0.1.0"),
                app_version=ai_response.get("app_version", "1.0.0"),
                keywords=ai_response.get("keywords", [])
            )

            # Parse templates
            templates_data = ai_response.get("templates", [])
            for template_data in templates_data:
                template = HelmTemplate(
                    name=template_data.get("name", ""),
                    template_type=self._determine_template_type(template_data.get("kind", "")),
                    content=template_data.get("content", ""),
                    values_schema=template_data.get("values_schema", {}),
                    notes=template_data.get("notes", []),
                    source_resources=template_data.get("source_resources", [])
                )
                helm_chart.templates.append(template)

            # Parse values
            values_data = ai_response.get("values", {})
            helm_chart.values = HelmValues(
                global_values=values_data.get("global", {}),
                application_values=values_data.get("application", {}),
                environment_values=values_data.get("environment", {}),
                schema=values_data.get("schema", {})
            )

            # Set additional properties
            helm_chart.helpers = ai_response.get("helpers", "")
            helm_chart.notes = ai_response.get("notes_txt", "")
            helm_chart.dependencies = ai_response.get("dependencies", [])
            helm_chart.conversion_notes = ai_response.get("conversion_notes", [])

            # Calculate metrics
            helm_chart.total_templates = len(helm_chart.templates)
            helm_chart.complexity_score = self._calculate_chart_complexity(helm_chart)

            return helm_chart

        except Exception as e:
            logger.error(f"Failed to parse AI response: {str(e)}")
            raise

    def _determine_template_type(self, kind: str) -> HelmTemplateType:
        """Determine Helm template type from Kubernetes resource kind."""
        kind_mapping = {
            "Deployment": HelmTemplateType.DEPLOYMENT,
            "Service": HelmTemplateType.SERVICE,
            "ConfigMap": HelmTemplateType.CONFIGMAP,
            "Secret": HelmTemplateType.SECRET,
            "Ingress": HelmTemplateType.INGRESS,
            "PersistentVolumeClaim": HelmTemplateType.PVC,
            "ServiceAccount": HelmTemplateType.SERVICE_ACCOUNT,
            "Role": HelmTemplateType.RBAC,
            "RoleBinding": HelmTemplateType.RBAC,
            "ClusterRole": HelmTemplateType.RBAC,
            "ClusterRoleBinding": HelmTemplateType.RBAC,
            "HorizontalPodAutoscaler": HelmTemplateType.HPA,
            "NetworkPolicy": HelmTemplateType.NETWORK_POLICY,
        }
        return kind_mapping.get(kind, HelmTemplateType.CUSTOM)

    async def _apply_post_processing(self, helm_chart: HelmChart, message: ContextDocsMessage):
        """Apply post-processing improvements to the generated chart."""
        try:
            # Validate YAML syntax if enabled
            if self.config.validate_generated_yaml:
                await self._validate_yaml_syntax(helm_chart)

            # Apply additional best practices
            if self.config.apply_best_practices:
                self._apply_helm_best_practices(helm_chart, message)

            # Generate documentation if enabled
            if self.config.generate_documentation:
                self._generate_chart_documentation(helm_chart)

            logger.info("Post-processing completed successfully")

        except Exception as e:
            logger.warning(f"Post-processing failed: {str(e)}")

    async def _validate_yaml_syntax(self, helm_chart: HelmChart):
        """Validate YAML syntax of generated templates."""
        for template in helm_chart.templates:
            try:
                yaml.safe_load(template.content)
            except yaml.YAMLError as e:
                logger.warning(f"YAML syntax error in template {template.name}: {str(e)}")
                # Attempt to fix common issues
                template.content = self._fix_common_yaml_issues(template.content)

    def _fix_common_yaml_issues(self, content: str) -> str:
        """Fix common YAML syntax issues."""
        # Remove trailing whitespace
        lines = [line.rstrip() for line in content.split('\n')]

        # Fix indentation issues (basic)
        fixed_lines = []
        for line in lines:
            if line.strip():
                # Ensure consistent indentation (2 spaces)
                indent_level = (len(line) - len(line.lstrip())) // 2
                fixed_line = '  ' * indent_level + line.lstrip()
                fixed_lines.append(fixed_line)
            else:
                fixed_lines.append('')

        return '\n'.join(fixed_lines)

    def _apply_helm_best_practices(self, helm_chart: HelmChart, message: ContextDocsMessage):
        """Apply Helm best practices to the generated chart."""
        # Add standard labels to all templates
        standard_labels = {
            "app.kubernetes.io/name": "{{ include \"" + helm_chart.name + ".name\" . }}",
            "app.kubernetes.io/instance": "{{ .Release.Name }}",
            "app.kubernetes.io/version": "{{ .Chart.AppVersion }}",
            "app.kubernetes.io/managed-by": "{{ .Release.Service }}"
        }

        # Apply to each template
        for template in helm_chart.templates:
            if "metadata:" in template.content and "labels:" not in template.content:
                # Add labels section if missing
                template.content = template.content.replace(
                    "metadata:",
                    f"metadata:\n  labels:\n" +
                    "\n".join([f"    {k}: {v}" for k, v in standard_labels.items()])
                )

    def _generate_chart_documentation(self, helm_chart: HelmChart):
        """Generate chart documentation."""
        # Generate NOTES.txt if not provided
        if not helm_chart.notes:
            helm_chart.notes = f"""
1. Get the application URL by running these commands:
{{- if .Values.ingress.enabled }}
{{- range $host := .Values.ingress.hosts }}
  {{- range .paths }}
  http{{- if $.Values.ingress.tls }}s{{- end }}://{{{{ $host.host }}}}{{{{ .path }}}}
  {{- end }}
{{- end }}
{{- else if contains "NodePort" .Values.service.type }}
  export NODE_PORT=$(kubectl get --namespace {{{{ .Release.Namespace }}}} -o jsonpath="{{.spec.ports[0].nodePort}}" services {{{{ include "{helm_chart.name}.fullname" . }}}})
  export NODE_IP=$(kubectl get nodes --namespace {{{{ .Release.Namespace }}}} -o jsonpath="{{.items[0].status.addresses[0].address}}")
  echo http://$NODE_IP:$NODE_PORT
{{- else if contains "LoadBalancer" .Values.service.type }}
     NOTE: It may take a few minutes for the LoadBalancer IP to be available.
           You can watch the status of by running 'kubectl get --namespace {{{{ .Release.Namespace }}}} svc -w {{{{ include "{helm_chart.name}.fullname" . }}}}'
  export SERVICE_IP=$(kubectl get svc --namespace {{{{ .Release.Namespace }}}} {{{{ include "{helm_chart.name}.fullname" . }}}} --template "{{{{ range (index .status.loadBalancer.ingress 0) }}}}{{{{.}}}}{{{{ end }}}}")
  echo http://$SERVICE_IP:{{{{ .Values.service.port }}}}
{{- else if contains "ClusterIP" .Values.service.type }}
  export POD_NAME=$(kubectl get pods --namespace {{{{ .Release.Namespace }}}} -l "app.kubernetes.io/name={{{{ include "{helm_chart.name}.name" . }}}},app.kubernetes.io/instance={{{{ .Release.Name }}}}" -o jsonpath="{{.items[0].metadata.name}}")
  export CONTAINER_PORT=$(kubectl get pod --namespace {{{{ .Release.Namespace }}}} $POD_NAME -o jsonpath="{{.spec.containers[0].ports[0].containerPort}}")
  echo "Visit http://127.0.0.1:8080 to use your application"
  kubectl --namespace {{{{ .Release.Namespace }}}} port-forward $POD_NAME 8080:$CONTAINER_PORT
{{- end }}
"""

    def _calculate_chart_complexity(self, helm_chart: HelmChart) -> float:
        """Calculate complexity score for the Helm chart."""
        score = 0.0

        # Base score from template count
        score += len(helm_chart.templates) * 5.0

        # Additional complexity from template types
        for template in helm_chart.templates:
            if template.template_type == HelmTemplateType.DEPLOYMENT:
                score += 10.0
            elif template.template_type == HelmTemplateType.RBAC:
                score += 15.0
            elif template.template_type == HelmTemplateType.CUSTOM:
                score += 20.0
            else:
                score += 5.0

        # Complexity from values structure
        def count_nested_values(values_dict):
            count = 0
            for value in values_dict.values():
                if isinstance(value, dict):
                    count += 1 + count_nested_values(value)
                else:
                    count += 1
            return count

        values_complexity = (
            count_nested_values(helm_chart.values.global_values) +
            count_nested_values(helm_chart.values.application_values) +
            count_nested_values(helm_chart.values.environment_values)
        )
        score += values_complexity * 2.0

        # Complexity from dependencies
        score += len(helm_chart.dependencies) * 10.0

        # Normalize to 0-100 scale
        return min(score / 10.0, 100.0)
