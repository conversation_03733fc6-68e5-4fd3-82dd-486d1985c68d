"""Main entry point for Linter Agent."""

import asyncio
import signal
import sys
from typing import Optional

from agents.linter.agent import LinterAgent
from agents.linter.models import LinterConfig
from shared.config.settings import settings
from shared.models.messages import DraftChartMessage
from shared.utils.logging import get_logger
from shared.utils.pubsub import pubsub_client
from shared.utils.mcp_client import mcp_ops

logger = get_logger(__name__)


class LinterAgentService:
    """Main service class for Linter Agent."""
    
    def __init__(self, config: Optional[LinterConfig] = None):
        self.config = config or LinterConfig()
        self.agent = LinterAgent(self.config)
        self.running = False
        self.subscriber = None

    async def start(self):
        """Start the Linter Agent service."""
        logger.info("Starting Linter Agent service")
        
        try:
            # Validate MCP setup
            await self._validate_mcp_setup()
            
            # Setup signal handlers
            self._setup_signal_handlers()
            
            # Start message processing
            await self._start_message_processing()
            
        except Exception as e:
            logger.error("Failed to start Linter Agent service", error=str(e), exc_info=True)
            sys.exit(1)

    async def _validate_mcp_setup(self):
        """Validate MCP server connections."""
        logger.info("Validating MCP setup for Linter Agent")
        
        try:
            # Test memory server
            await mcp_ops.store_memory("linter_setup_test", "validation")
            test_value = await mcp_ops.retrieve_memory("linter_setup_test")
            if test_value != "validation":
                raise Exception("Memory server validation failed")
            
            # Test filesystem server
            try:
                test_dir = "/tmp/linter_test"
                await mcp_ops.client.call_tool("filesystem", "create_directory", {
                    "path": test_dir
                })
                await mcp_ops.client.call_tool("filesystem", "delete_directory", {
                    "path": test_dir,
                    "recursive": True
                })
                logger.info("Filesystem server validated")
            except Exception as e:
                logger.warning(f"Filesystem server validation failed: {str(e)}")
            
            logger.info("MCP setup validation completed")
            
        except Exception as e:
            logger.error(f"MCP setup validation failed: {str(e)}")
            raise

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown")
            self.running = False
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def _start_message_processing(self):
        """Start processing messages from Pub/Sub."""
        logger.info("Starting message processing")
        self.running = True
        
        # Create subscriber
        self.subscriber = pubsub_client.create_subscriber(
            settings.pubsub.topic_helm_charts,
            self._process_message_callback
        )
        
        # Process messages
        while self.running:
            try:
                # Pull messages with timeout
                messages = await self.subscriber.pull_messages(
                    max_messages=1,
                    timeout_seconds=30
                )
                
                if not messages:
                    continue
                    
                for message in messages:
                    await self._handle_message(message)
                    
            except Exception as e:
                logger.error("Error in message processing loop", error=str(e), exc_info=True)
                await asyncio.sleep(5)  # Brief pause before retrying

    async def _handle_message(self, message):
        """Handle a single Pub/Sub message."""
        try:
            # Parse message
            charts_message = DraftChartMessage.model_validate_json(message.data)
            
            logger.info(
                "Processing helm charts message",
                job_id=str(charts_message.job_id),
                repository=charts_message.repository.name,
                chart_name=charts_message.helm_chart.name,
                templates_count=charts_message.helm_chart.total_templates,
                chart_complexity=charts_message.helm_chart.complexity_score
            )
            
            # Process with agent
            result = await self.agent.process_message(charts_message)
            
            if result.success:
                logger.info(
                    "Successfully processed message",
                    job_id=str(charts_message.job_id),
                    duration=result.duration_seconds,
                    quality_score=result.quality_score,
                    issues_found=result.issues_found,
                    tests_passed=result.tests_passed
                )
                message.ack()
            else:
                logger.error(
                    "Failed to process message",
                    job_id=str(charts_message.job_id),
                    error=result.error_message
                )
                message.nack()
                
        except Exception as e:
            logger.error("Error handling message", error=str(e), exc_info=True)
            message.nack()

    async def _process_message_callback(self, message):
        """Callback for processing Pub/Sub messages."""
        await self._handle_message(message)

    async def stop(self):
        """Stop the Linter Agent service."""
        logger.info("Stopping Linter Agent service")
        self.running = False
        
        if self.subscriber:
            await self.subscriber.close()

    async def health_check(self) -> dict:
        """Perform service health check."""
        try:
            # Test MCP connections
            await mcp_ops.store_memory("linter_health_check", "ok")
            
            return {
                "status": "healthy",
                "service": "linter-agent",
                "version": "1.0.0",
                "running": self.running,
                "mcp_servers": len(self.agent.mcp_servers),
                "validation_tools": {
                    "helm_lint": self.config.enable_helm_lint,
                    "kubeval": self.config.enable_kubeval,
                    "chart_testing": self.config.enable_chart_testing,
                    "security_scan": self.config.enable_security_scan,
                    "performance_check": self.config.enable_performance_check
                },
                "quality_thresholds": {
                    "min_quality_score": self.config.min_quality_score,
                    "min_security_score": self.config.min_security_score,
                    "min_performance_score": self.config.min_performance_score
                }
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "service": "linter-agent",
                "error": str(e)
            }


async def main():
    """Main entry point."""
    logger.info("Initializing Linter Agent")
    
    try:
        # Load configuration
        config = LinterConfig()
        
        # Create and start service
        service = LinterAgentService(config)
        await service.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down")
    except Exception as e:
        logger.error("Fatal error in main", error=str(e), exc_info=True)
        sys.exit(1)


def cli_main():
    """CLI entry point."""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error("Application failed", error=str(e), exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    cli_main()
