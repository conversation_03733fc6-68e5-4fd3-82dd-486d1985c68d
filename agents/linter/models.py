"""Data models for Linter Agent."""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class ValidationSeverity(str, Enum):
    """Severity levels for validation issues."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ValidationCategory(str, Enum):
    """Categories of validation checks."""
    SYNTAX = "syntax"
    STRUCTURE = "structure"
    SECURITY = "security"
    PERFORMANCE = "performance"
    BEST_PRACTICES = "best_practices"
    COMPATIBILITY = "compatibility"
    RESOURCES = "resources"
    VALUES = "values"
    TEMPLATES = "templates"


class ValidationIssue(BaseModel):
    """Represents a validation issue found in the Helm chart."""
    
    category: ValidationCategory = Field(..., description="Issue category")
    severity: ValidationSeverity = Field(..., description="Issue severity")
    title: str = Field(..., description="Issue title")
    description: str = Field(..., description="Detailed description")
    file_path: Optional[str] = Field(default=None, description="File where issue was found")
    line_number: Optional[int] = Field(default=None, description="Line number of issue")
    suggestion: Optional[str] = Field(default=None, description="Suggested fix")
    rule_id: str = Field(..., description="Validation rule identifier")
    documentation_url: Optional[str] = Field(default=None, description="Link to documentation")


class TestResult(BaseModel):
    """Represents a test execution result."""
    
    test_name: str = Field(..., description="Name of the test")
    test_type: str = Field(..., description="Type of test (lint, template, install)")
    success: bool = Field(..., description="Whether test passed")
    duration_seconds: float = Field(..., description="Test execution time")
    output: str = Field(default="", description="Test output")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")


class ChartValidationResult(BaseModel):
    """Complete validation result for a Helm chart."""
    
    # Overall status
    valid: bool = Field(..., description="Whether chart is valid")
    score: float = Field(..., description="Overall quality score (0-100)")
    
    # Issues found
    issues: List[ValidationIssue] = Field(default_factory=list, description="All validation issues")
    critical_issues: int = Field(default=0, description="Number of critical issues")
    error_issues: int = Field(default=0, description="Number of error issues")
    warning_issues: int = Field(default=0, description="Number of warning issues")
    info_issues: int = Field(default=0, description="Number of info issues")
    
    # Test results
    test_results: List[TestResult] = Field(default_factory=list, description="Test execution results")
    tests_passed: int = Field(default=0, description="Number of tests passed")
    tests_failed: int = Field(default=0, description="Number of tests failed")
    
    # Metrics
    total_templates: int = Field(default=0, description="Total number of templates")
    templates_validated: int = Field(default=0, description="Number of templates validated")
    values_validated: bool = Field(default=False, description="Whether values.yaml was validated")
    
    # Recommendations
    recommendations: List[str] = Field(default_factory=list, description="Improvement recommendations")
    security_score: float = Field(default=0.0, description="Security score (0-100)")
    performance_score: float = Field(default=0.0, description="Performance score (0-100)")
    maintainability_score: float = Field(default=0.0, description="Maintainability score (0-100)")


class LinterConfig(BaseModel):
    """Configuration for Linter Agent."""
    
    # Validation settings
    enable_helm_lint: bool = Field(default=True, description="Enable helm lint validation")
    enable_kubeval: bool = Field(default=True, description="Enable kubeval validation")
    enable_chart_testing: bool = Field(default=True, description="Enable chart-testing")
    enable_security_scan: bool = Field(default=True, description="Enable security scanning")
    enable_performance_check: bool = Field(default=True, description="Enable performance checks")
    
    # Kubernetes versions to validate against
    kubernetes_versions: List[str] = Field(
        default_factory=lambda: ["1.25", "1.26", "1.27", "1.28"],
        description="Kubernetes versions to validate against"
    )
    
    # Severity thresholds
    fail_on_critical: bool = Field(default=True, description="Fail validation on critical issues")
    fail_on_error: bool = Field(default=True, description="Fail validation on error issues")
    fail_on_warning: bool = Field(default=False, description="Fail validation on warning issues")
    max_warnings: int = Field(default=10, description="Maximum allowed warnings")
    
    # Quality thresholds
    min_quality_score: float = Field(default=70.0, description="Minimum quality score to pass")
    min_security_score: float = Field(default=80.0, description="Minimum security score")
    min_performance_score: float = Field(default=70.0, description="Minimum performance score")
    
    # Test configuration
    test_timeout_seconds: int = Field(default=300, description="Test timeout in seconds")
    parallel_tests: bool = Field(default=True, description="Run tests in parallel")
    cleanup_after_tests: bool = Field(default=True, description="Cleanup resources after tests")
    
    # Custom rules
    custom_rules_enabled: bool = Field(default=True, description="Enable custom validation rules")
    custom_rules_path: Optional[str] = Field(default=None, description="Path to custom rules file")
    
    # Output configuration
    generate_report: bool = Field(default=True, description="Generate validation report")
    report_format: str = Field(default="json", description="Report format (json, yaml, html)")
    include_suggestions: bool = Field(default=True, description="Include fix suggestions in report")
    
    # Tool configurations
    helm_version: str = Field(default="3.12.0", description="Helm version to use")
    kubeval_version: str = Field(default="0.16.1", description="Kubeval version to use")
    chart_testing_version: str = Field(default="3.8.0", description="Chart-testing version to use")
