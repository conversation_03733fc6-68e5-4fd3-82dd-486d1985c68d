"""Data models for Parser Agent."""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class KustomizeResourceType(str, Enum):
    """Types of Kubernetes resources found in Kustomize."""
    DEPLOYMENT = "Deployment"
    SERVICE = "Service"
    CONFIGMAP = "ConfigMap"
    SECRET = "Secret"
    INGRESS = "Ingress"
    PERSISTENT_VOLUME_CLAIM = "PersistentVolumeClaim"
    SERVICE_ACCOUNT = "ServiceAccount"
    ROLE = "Role"
    ROLE_BINDING = "RoleBinding"
    CLUSTER_ROLE = "ClusterRole"
    CLUSTER_ROLE_BINDING = "ClusterRoleBinding"
    HORIZONTAL_POD_AUTOSCALER = "HorizontalPodAutoscaler"
    NETWORK_POLICY = "NetworkPolicy"
    CUSTOM_RESOURCE = "CustomResource"
    UNKNOWN = "Unknown"


class KustomizeResource(BaseModel):
    """Represents a parsed Kubernetes resource from Kustomize."""
    
    api_version: str = Field(..., description="Kubernetes API version")
    kind: str = Field(..., description="Kubernetes resource kind")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Resource metadata")
    spec: Optional[Dict[str, Any]] = Field(default=None, description="Resource specification")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Resource data (for ConfigMaps/Secrets)")
    resource_type: KustomizeResourceType = Field(..., description="Classified resource type")
    source_file: str = Field(..., description="Source file path")
    namespace: Optional[str] = Field(default=None, description="Resource namespace")


class KustomizeTransformation(BaseModel):
    """Represents a Kustomize transformation."""
    
    type: str = Field(..., description="Transformation type (patch, overlay, etc.)")
    target: Dict[str, Any] = Field(..., description="Target resource selector")
    operations: List[Dict[str, Any]] = Field(default_factory=list, description="Transformation operations")
    source_file: str = Field(..., description="Source kustomization file")


class KustomizeBase(BaseModel):
    """Represents a Kustomize base configuration."""
    
    path: str = Field(..., description="Base path")
    resources: List[KustomizeResource] = Field(default_factory=list, description="Base resources")
    kustomization_file: str = Field(..., description="Kustomization file path")


class ParsedKustomizeStructure(BaseModel):
    """Complete parsed structure of a Kustomize configuration."""
    
    root_path: str = Field(..., description="Root kustomization path")
    kustomization_file: str = Field(..., description="Main kustomization file")
    
    # Resources
    resources: List[KustomizeResource] = Field(default_factory=list, description="All parsed resources")
    
    # Bases and overlays
    bases: List[KustomizeBase] = Field(default_factory=list, description="Base configurations")
    overlays: List[str] = Field(default_factory=list, description="Overlay paths")
    
    # Transformations
    transformations: List[KustomizeTransformation] = Field(default_factory=list, description="Applied transformations")
    
    # Configuration
    namespace: Optional[str] = Field(default=None, description="Default namespace")
    name_prefix: Optional[str] = Field(default=None, description="Name prefix")
    name_suffix: Optional[str] = Field(default=None, description="Name suffix")
    common_labels: Dict[str, str] = Field(default_factory=dict, description="Common labels")
    common_annotations: Dict[str, str] = Field(default_factory=dict, description="Common annotations")
    
    # Images
    images: List[Dict[str, str]] = Field(default_factory=list, description="Image configurations")
    
    # Generators
    config_map_generator: List[Dict[str, Any]] = Field(default_factory=list, description="ConfigMap generators")
    secret_generator: List[Dict[str, Any]] = Field(default_factory=list, description="Secret generators")
    
    # Metadata
    total_resources: int = Field(default=0, description="Total number of resources")
    resource_types: Dict[str, int] = Field(default_factory=dict, description="Count by resource type")
    complexity_score: float = Field(default=0.0, description="Complexity assessment score")


class ParserConfig(BaseModel):
    """Configuration for Parser Agent."""
    
    max_file_size_mb: int = Field(default=10, description="Maximum file size to process (MB)")
    max_resources_per_file: int = Field(default=100, description="Maximum resources per file")
    supported_api_versions: List[str] = Field(
        default_factory=lambda: [
            "v1", "apps/v1", "extensions/v1beta1", "networking.k8s.io/v1",
            "rbac.authorization.k8s.io/v1", "autoscaling/v1", "autoscaling/v2"
        ],
        description="Supported Kubernetes API versions"
    )
    ignore_unknown_resources: bool = Field(default=True, description="Ignore unknown resource types")
    validate_yaml_syntax: bool = Field(default=True, description="Validate YAML syntax")
    extract_dependencies: bool = Field(default=True, description="Extract resource dependencies")
    calculate_complexity: bool = Field(default=True, description="Calculate complexity scores")
    timeout_seconds: int = Field(default=300, description="Processing timeout")
