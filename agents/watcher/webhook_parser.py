"""Webhook payload parsing for different Git providers."""

import hashlib
import hmac
from datetime import datetime
from typing import Optional

from fastapi import HTTPException

from agents.watcher.models import (
    EventType,
    GitHubWebhookPayload,
    ********************,
    WebhookEvent,
    WebhookProvider,
)
from shared.config.settings import settings
from shared.utils.logging import get_logger

logger = get_logger(__name__)


class WebhookParser:
    """Parser for webhook payloads from different Git providers."""
    
    def __init__(self):
        self.github_secret = settings.repository.github_webhook_secret
        self.gitlab_secret = settings.repository.*********************
    
    def verify_github_signature(self, payload: bytes, signature: str) -> bool:
        """Verify GitHub webhook signature.
        
        Args:
            payload: Raw webhook payload
            signature: GitHub signature header
            
        Returns:
            True if signature is valid
        """
        if not self.github_secret:
            logger.warning("GitHub webhook secret not configured")
            return True  # Skip verification if no secret configured
        
        if not signature.startswith('sha256='):
            return False
        
        expected_signature = hmac.new(
            self.github_secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        received_signature = signature[7:]  # Remove 'sha256=' prefix
        
        return hmac.compare_digest(expected_signature, received_signature)
    
    def verify_gitlab_signature(self, payload: bytes, token: str) -> bool:
        """Verify GitLab webhook token.
        
        Args:
            payload: Raw webhook payload
            token: GitLab token header
            
        Returns:
            True if token is valid
        """
        if not self.gitlab_secret:
            logger.warning("GitLab webhook secret not configured")
            return True  # Skip verification if no secret configured
        
        return hmac.compare_digest(self.gitlab_secret, token)
    
    def parse_github_webhook(self, payload: dict) -> Optional[WebhookEvent]:
        """Parse GitHub webhook payload.
        
        Args:
            payload: GitHub webhook payload
            
        Returns:
            Parsed webhook event or None
        """
        try:
            github_payload = GitHubWebhookPayload.model_validate(payload)
            
            # Determine event type
            event_type = EventType.PUSH
            if 'pull_request' in payload:
                event_type = EventType.PULL_REQUEST
            
            # Extract repository information
            repo = github_payload.repository
            repository_url = repo.get('clone_url', repo.get('html_url', ''))
            repository_name = repo.get('full_name', '')
            
            # Extract commit information
            head_commit = github_payload.head_commit
            if not head_commit and github_payload.commits:
                head_commit = github_payload.commits[-1]  # Use latest commit
            
            if not head_commit:
                logger.warning("No commit information in GitHub webhook")
                return None
            
            # Extract branch from ref
            ref = github_payload.ref or ''
            branch = ref.replace('refs/heads/', '') if ref.startswith('refs/heads/') else 'main'
            
            # Extract changed files
            files_changed = []
            for commit in github_payload.commits:
                files_changed.extend(commit.get('added', []))
                files_changed.extend(commit.get('modified', []))
                files_changed.extend(commit.get('removed', []))
            
            return WebhookEvent(
                provider=WebhookProvider.GITHUB,
                event_type=event_type,
                repository_url=repository_url,
                repository_name=repository_name,
                branch=branch,
                commit_sha=head_commit.get('id', ''),
                commit_message=head_commit.get('message', ''),
                author_name=head_commit.get('author', {}).get('name', ''),
                author_email=head_commit.get('author', {}).get('email', ''),
                timestamp=datetime.fromisoformat(
                    head_commit.get('timestamp', datetime.utcnow().isoformat()).replace('Z', '+00:00')
                ),
                files_changed=list(set(files_changed)),  # Remove duplicates
                raw_payload=payload
            )
            
        except Exception as e:
            logger.error("Failed to parse GitHub webhook", error=str(e), exc_info=True)
            return None
    
    def parse_gitlab_webhook(self, payload: dict) -> Optional[WebhookEvent]:
        """Parse GitLab webhook payload.
        
        Args:
            payload: GitLab webhook payload
            
        Returns:
            Parsed webhook event or None
        """
        try:
            gitlab_payload = ********************.model_validate(payload)
            
            # Determine event type
            event_type = EventType.PUSH
            if gitlab_payload.object_kind == 'merge_request':
                event_type = EventType.MERGE_REQUEST
            elif gitlab_payload.object_kind == 'tag_push':
                event_type = EventType.TAG
            
            # Extract repository information
            project = gitlab_payload.project
            repository_url = project.get('git_http_url', project.get('web_url', ''))
            repository_name = project.get('path_with_namespace', '')
            
            # Extract commit information
            commit_sha = gitlab_payload.after or gitlab_payload.checkout_sha
            if not commit_sha and gitlab_payload.commits:
                commit_sha = gitlab_payload.commits[-1].get('id', '')
            
            if not commit_sha:
                logger.warning("No commit information in GitLab webhook")
                return None
            
            # Extract branch from ref
            ref = gitlab_payload.ref or ''
            branch = ref.replace('refs/heads/', '') if ref.startswith('refs/heads/') else 'main'
            
            # Extract commit details
            commit_message = ''
            author_name = gitlab_payload.user_name or ''
            author_email = gitlab_payload.user_email or ''
            timestamp = datetime.utcnow()
            
            if gitlab_payload.commits:
                latest_commit = gitlab_payload.commits[-1]
                commit_message = latest_commit.get('message', '')
                author_name = latest_commit.get('author', {}).get('name', author_name)
                author_email = latest_commit.get('author', {}).get('email', author_email)
                if 'timestamp' in latest_commit:
                    timestamp = datetime.fromisoformat(
                        latest_commit['timestamp'].replace('Z', '+00:00')
                    )
            
            # Extract changed files
            files_changed = []
            for commit in gitlab_payload.commits:
                files_changed.extend(commit.get('added', []))
                files_changed.extend(commit.get('modified', []))
                files_changed.extend(commit.get('removed', []))
            
            return WebhookEvent(
                provider=WebhookProvider.GITLAB,
                event_type=event_type,
                repository_url=repository_url,
                repository_name=repository_name,
                branch=branch,
                commit_sha=commit_sha,
                commit_message=commit_message,
                author_name=author_name,
                author_email=author_email,
                timestamp=timestamp,
                files_changed=list(set(files_changed)),  # Remove duplicates
                raw_payload=payload
            )
            
        except Exception as e:
            logger.error("Failed to parse GitLab webhook", error=str(e), exc_info=True)
            return None
    
    def parse_webhook(
        self, 
        payload: dict, 
        headers: dict,
        raw_payload: bytes
    ) -> WebhookEvent:
        """Parse webhook payload based on headers.
        
        Args:
            payload: Webhook payload
            headers: HTTP headers
            raw_payload: Raw payload bytes for signature verification
            
        Returns:
            Parsed webhook event
            
        Raises:
            HTTPException: If parsing fails or signature is invalid
        """
        # Determine provider from headers
        user_agent = headers.get('user-agent', '').lower()
        github_event = headers.get('x-github-event')
        gitlab_event = headers.get('x-gitlab-event')
        
        if github_event or 'github' in user_agent:
            # Verify GitHub signature
            signature = headers.get('x-hub-signature-256', '')
            if not self.verify_github_signature(raw_payload, signature):
                raise HTTPException(status_code=401, detail="Invalid GitHub signature")
            
            event = self.parse_github_webhook(payload)
            if not event:
                raise HTTPException(status_code=400, detail="Failed to parse GitHub webhook")
            
            return event
            
        elif gitlab_event or 'gitlab' in user_agent:
            # Verify GitLab token
            token = headers.get('x-gitlab-token', '')
            if not self.verify_gitlab_signature(raw_payload, token):
                raise HTTPException(status_code=401, detail="Invalid GitLab token")
            
            event = self.parse_gitlab_webhook(payload)
            if not event:
                raise HTTPException(status_code=400, detail="Failed to parse GitLab webhook")
            
            return event
            
        else:
            raise HTTPException(
                status_code=400, 
                detail="Unsupported webhook provider"
            )
    
    def should_process_event(self, event: WebhookEvent) -> bool:
        """Determine if an event should be processed.
        
        Args:
            event: Webhook event
            
        Returns:
            True if event should be processed
        """
        # Only process push events for now
        if event.event_type != EventType.PUSH:
            logger.info(
                "Skipping non-push event",
                event_type=event.event_type.value,
                repository=event.repository_name
            )
            return False
        
        # Check if any Kustomize files were changed
        kustomize_files = [
            'kustomization.yaml',
            'kustomization.yml',
            'Kustomization'
        ]
        
        has_kustomize_changes = any(
            any(kustomize_file in file_path for kustomize_file in kustomize_files)
            for file_path in event.files_changed
        )
        
        if not has_kustomize_changes:
            logger.info(
                "No Kustomize files changed, skipping",
                repository=event.repository_name,
                files_changed=event.files_changed
            )
            return False
        
        return True
