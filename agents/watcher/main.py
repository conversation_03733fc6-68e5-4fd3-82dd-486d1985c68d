"""Main entry point for the Watcher Agent."""

import asyncio
import signal
import sys
from typing import Optional

import uvicorn
from fastapi import FastAPI

from agents.watcher.models import WatcherConfig
from agents.watcher.webhook_server import app
from shared.config.mcp_servers import mcp_config
from shared.utils.logging import get_logger
from shared.utils.mcp_client import mcp_ops

logger = get_logger(__name__)


class WatcherAgentService:
    """Main service class for the Watcher Agent."""
    
    def __init__(self, config: Optional[WatcherConfig] = None):
        self.config = config or WatcherConfig()
        self.server: Optional[uvicorn.Server] = None
        self.running = False
    
    async def start(self):
        """Start the Watcher Agent service."""
        logger.info("Starting Watcher Agent service")
        
        try:
            # Validate MCP configuration
            await self._validate_mcp_setup()
            
            # Setup signal handlers
            self._setup_signal_handlers()
            
            # Start webhook server
            await self._start_webhook_server()
            
        except Exception as e:
            logger.error("Failed to start Watcher Agent service", error=str(e), exc_info=True)
            sys.exit(1)
    
    async def _validate_mcp_setup(self):
        """Validate MCP server setup."""
        logger.info("Validating MCP server setup")
        
        required_servers = mcp_config.get_servers_for_agent("watcher")
        
        for server_config in required_servers:
            try:
                logger.info(f"Testing MCP server: {server_config.name}")
                
                # Test basic connection
                if server_config.name == "memory":
                    await mcp_ops.store_memory("setup_test", "validation")
                    test_value = await mcp_ops.retrieve_memory("setup_test")
                    if test_value != "validation":
                        raise Exception("Memory server validation failed")
                
                elif server_config.name == "filesystem":
                    # Test filesystem operations
                    await mcp_ops.client.call_tool("filesystem", "create_directory", {
                        "path": mcp_config.workspace_root
                    })
                
                elif server_config.name == "git":
                    # Test git server availability
                    tools = await mcp_ops.client.list_tools("git")
                    if not any(tool.get("name") == "clone" for tool in tools):
                        raise Exception("Git clone tool not available")
                
                logger.info(f"MCP server validation successful: {server_config.name}")
                
            except Exception as e:
                logger.error(
                    f"MCP server validation failed: {server_config.name}",
                    error=str(e),
                    exc_info=True
                )
                if server_config.name in ["memory", "filesystem"]:
                    # These are critical servers
                    raise Exception(f"Critical MCP server failed: {server_config.name}")
                else:
                    logger.warning(f"Non-critical MCP server failed: {server_config.name}")
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def _start_webhook_server(self):
        """Start the webhook server."""
        logger.info(
            "Starting webhook server",
            host=self.config.webhook_host,
            port=self.config.webhook_port
        )
        
        config = uvicorn.Config(
            app,
            host=self.config.webhook_host,
            port=self.config.webhook_port,
            log_level="info",
            access_log=True,
            loop="asyncio"
        )
        
        self.server = uvicorn.Server(config)
        self.running = True
        
        try:
            await self.server.serve()
        except Exception as e:
            logger.error("Webhook server error", error=str(e), exc_info=True)
            raise
    
    async def stop(self):
        """Stop the Watcher Agent service."""
        if not self.running:
            return
        
        logger.info("Stopping Watcher Agent service")
        self.running = False
        
        try:
            # Stop webhook server
            if self.server:
                self.server.should_exit = True
                await self.server.shutdown()
            
            # Close MCP connections
            await mcp_ops.client.close_all_sessions()
            
            logger.info("Watcher Agent service stopped successfully")
            
        except Exception as e:
            logger.error("Error during service shutdown", error=str(e), exc_info=True)
    
    async def health_check(self) -> dict:
        """Perform service health check."""
        try:
            # Test MCP connections
            await mcp_ops.store_memory("health_check", "ok")
            
            return {
                "status": "healthy",
                "service": "watcher-agent",
                "version": "1.0.0",
                "mcp_servers": len(mcp_config.get_servers_for_agent("watcher")),
                "webhook_server": "running" if self.running else "stopped"
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "service": "watcher-agent",
                "error": str(e)
            }


async def main():
    """Main entry point."""
    logger.info("Initializing Watcher Agent")
    
    try:
        # Load configuration
        config = WatcherConfig()
        
        # Create and start service
        service = WatcherAgentService(config)
        await service.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down")
    except Exception as e:
        logger.error("Fatal error in main", error=str(e), exc_info=True)
        sys.exit(1)


def cli_main():
    """CLI entry point."""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error("Application failed", error=str(e), exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    cli_main()
