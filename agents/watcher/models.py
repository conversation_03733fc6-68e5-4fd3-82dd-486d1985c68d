"""Models specific to the Watcher Agent."""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class WebhookProvider(str, Enum):
    """Supported webhook providers."""
    
    GITHUB = "github"
    GITLAB = "gitlab"
    BITBUCKET = "bitbucket"
    AZURE_DEVOPS = "azure_devops"


class EventType(str, Enum):
    """Repository event types."""
    
    PUSH = "push"
    PULL_REQUEST = "pull_request"
    MERGE_REQUEST = "merge_request"
    TAG = "tag"


class WebhookEvent(BaseModel):
    """Represents a webhook event from a repository."""
    
    provider: WebhookProvider = Field(..., description="Webhook provider")
    event_type: EventType = Field(..., description="Type of event")
    repository_url: str = Field(..., description="Repository URL")
    repository_name: str = Field(..., description="Repository name")
    branch: str = Field(..., description="Branch name")
    commit_sha: str = Field(..., description="Commit SHA")
    commit_message: str = Field(..., description="Commit message")
    author_name: str = Field(..., description="Author name")
    author_email: str = Field(..., description="Author email")
    timestamp: datetime = Field(..., description="Event timestamp")
    files_changed: List[str] = Field(default_factory=list, description="Changed files")
    raw_payload: Dict[str, Any] = Field(default_factory=dict, description="Raw webhook payload")


class KustomizationDetection(BaseModel):
    """Result of kustomization detection in a repository."""
    
    path: str = Field(..., description="Path to kustomization.yaml")
    is_valid: bool = Field(..., description="Whether the kustomization is valid")
    base_directory: str = Field(..., description="Base directory of the kustomization")
    resources_count: int = Field(default=0, description="Number of resources")
    has_patches: bool = Field(default=False, description="Whether it has patches")
    has_generators: bool = Field(default=False, description="Whether it has generators")
    dependencies: List[str] = Field(default_factory=list, description="Dependencies/bases")
    error_message: Optional[str] = Field(default=None, description="Error if invalid")


class RepositoryAnalysis(BaseModel):
    """Analysis result of a repository for Kustomize configurations."""
    
    repository_url: str = Field(..., description="Repository URL")
    branch: str = Field(..., description="Branch analyzed")
    commit_sha: str = Field(..., description="Commit SHA analyzed")
    analysis_timestamp: datetime = Field(default_factory=datetime.utcnow)
    kustomizations: List[KustomizationDetection] = Field(
        default_factory=list, 
        description="Detected kustomizations"
    )
    total_kustomizations: int = Field(default=0, description="Total kustomizations found")
    valid_kustomizations: int = Field(default=0, description="Valid kustomizations")
    analysis_duration_seconds: float = Field(default=0.0, description="Analysis duration")
    errors: List[str] = Field(default_factory=list, description="Analysis errors")


class WatcherConfig(BaseModel):
    """Configuration for the Watcher Agent."""
    
    webhook_port: int = Field(default=8000, description="Webhook server port")
    webhook_path: str = Field(default="/webhook", description="Webhook endpoint path")
    github_secret: Optional[str] = Field(default=None, description="GitHub webhook secret")
    gitlab_secret: Optional[str] = Field(default=None, description="GitLab webhook secret")
    supported_file_patterns: List[str] = Field(
        default_factory=lambda: [
            "kustomization.yaml",
            "kustomization.yml",
            "Kustomization"
        ],
        description="Supported kustomization file patterns"
    )
    ignore_paths: List[str] = Field(
        default_factory=lambda: [
            ".git/",
            "node_modules/",
            "vendor/",
            ".terraform/",
            "target/",
            "build/"
        ],
        description="Paths to ignore during analysis"
    )
    max_analysis_depth: int = Field(default=10, description="Maximum directory depth to analyze")
    analysis_timeout_seconds: int = Field(default=300, description="Analysis timeout")
    batch_processing: bool = Field(default=True, description="Enable batch processing")
    batch_size: int = Field(default=10, description="Batch size for processing")
    retry_attempts: int = Field(default=3, description="Number of retry attempts")
    retry_delay_seconds: int = Field(default=5, description="Delay between retries")


class ProcessingStatus(BaseModel):
    """Status of webhook processing."""
    
    webhook_id: str = Field(..., description="Webhook ID")
    repository_url: str = Field(..., description="Repository URL")
    commit_sha: str = Field(..., description="Commit SHA")
    status: str = Field(..., description="Processing status")
    started_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = Field(default=None)
    jobs_created: List[str] = Field(default_factory=list, description="Created job IDs")
    error_message: Optional[str] = Field(default=None)
    retry_count: int = Field(default=0, description="Number of retries")


class GitHubWebhookPayload(BaseModel):
    """GitHub webhook payload structure."""
    
    action: Optional[str] = Field(default=None)
    repository: Dict[str, Any] = Field(default_factory=dict)
    commits: List[Dict[str, Any]] = Field(default_factory=list)
    head_commit: Optional[Dict[str, Any]] = Field(default=None)
    ref: Optional[str] = Field(default=None)
    before: Optional[str] = Field(default=None)
    after: Optional[str] = Field(default=None)
    pusher: Optional[Dict[str, Any]] = Field(default=None)
    sender: Optional[Dict[str, Any]] = Field(default=None)


class GitLabWebhookPayload(BaseModel):
    """GitLab webhook payload structure."""
    
    object_kind: str = Field(..., description="Event type")
    project: Dict[str, Any] = Field(default_factory=dict)
    commits: List[Dict[str, Any]] = Field(default_factory=list)
    ref: Optional[str] = Field(default=None)
    before: Optional[str] = Field(default=None)
    after: Optional[str] = Field(default=None)
    user_name: Optional[str] = Field(default=None)
    user_email: Optional[str] = Field(default=None)
    checkout_sha: Optional[str] = Field(default=None)
