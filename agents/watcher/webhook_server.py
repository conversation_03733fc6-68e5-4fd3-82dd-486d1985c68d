"""Webhook server for the Watcher Agent using MCP servers."""

from datetime import datetime, timezone
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, BackgroundTasks
from fastapi.responses import JSONResponse

from agents.watcher.agent import WatcherAgent
from agents.watcher.models import WatcherConfig
from agents.watcher.webhook_parser import WebhookParser
from shared.utils.logging import get_logger
from shared.utils.mcp_client import mcp_ops

logger = get_logger(__name__)

app = FastAPI(
    title="Kube2Helm Watcher Agent",
    description="Webhook server for monitoring repository changes and triggering Kustomize-to-Helm conversions",
    version="1.0.0"
)

# Initialize components
config = WatcherConfig()
agent = WatcherAgent(config)
parser = WebhookParser()


@app.on_event("startup")
async def startup_event():
    """Initialize MCP connections on startup."""
    logger.info("Starting Watcher Agent webhook server")
    
    # Test MCP server connections
    try:
        # Test memory server
        await mcp_ops.store_memory("startup_test", "Watcher agent started")
        logger.info("MCP memory server connection successful")
        
        # Test filesystem server
        await mcp_ops.client.call_tool("filesystem", "create_directory", {
            "path": "/tmp/kube2helm_workspaces"
        })
        logger.info("MCP filesystem server connection successful")
        
    except Exception as e:
        logger.error("Failed to initialize MCP connections", error=str(e), exc_info=True)


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up MCP connections on shutdown."""
    logger.info("Shutting down Watcher Agent webhook server")
    await mcp_ops.client.close_all_sessions()


@app.post("/webhook")
async def webhook_handler(
    request: Request,
    background_tasks: BackgroundTasks
) -> JSONResponse:
    """Handle incoming webhooks from Git providers.
    
    Args:
        request: HTTP request
        background_tasks: FastAPI background tasks
        
    Returns:
        JSON response
    """
    try:
        # Get request data
        raw_payload = await request.body()
        headers = dict(request.headers)
        
        try:
            payload = await request.json()
        except Exception as e:
            logger.error("Failed to parse JSON payload", error=str(e))
            raise HTTPException(status_code=400, detail="Invalid JSON payload")
        
        # Parse webhook event
        try:
            event = parser.parse_webhook(payload, headers, raw_payload)
        except HTTPException:
            raise
        except Exception as e:
            logger.error("Failed to parse webhook", error=str(e), exc_info=True)
            raise HTTPException(status_code=400, detail="Failed to parse webhook")
        
        # Check if event should be processed
        if not parser.should_process_event(event):
            return JSONResponse(
                status_code=200,
                content={
                    "status": "ignored",
                    "message": "Event does not require processing",
                    "event_type": event.event_type.value,
                    "repository": event.repository_name
                }
            )
        
        # Store webhook event in memory for tracking
        await mcp_ops.store_memory(f"webhook_{event.repository_name}_{event.commit_sha}", {
            "event": event.model_dump(),
            "status": "received",
            "timestamp": event.timestamp.isoformat()
        })
        
        # Process event in background
        background_tasks.add_task(process_webhook_background, event)
        
        return JSONResponse(
            status_code=202,
            content={
                "status": "accepted",
                "message": "Webhook event queued for processing",
                "event_type": event.event_type.value,
                "repository": event.repository_name,
                "commit_sha": event.commit_sha
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Webhook handler error", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


async def process_webhook_background(event):
    """Process webhook event in background using MCP servers.
    
    Args:
        event: Webhook event to process
    """
    try:
        logger.info(
            "Processing webhook event",
            repository=event.repository_name,
            commit_sha=event.commit_sha,
            event_type=event.event_type.value
        )
        
        # Update status in memory
        await mcp_ops.store_memory(f"webhook_{event.repository_name}_{event.commit_sha}", {
            "event": event.model_dump(),
            "status": "processing",
            "timestamp": event.timestamp.isoformat()
        })
        
        # Process with agent
        result = await agent.process_webhook_event(event)
        
        # Store result in memory
        await mcp_ops.store_memory(f"result_{event.repository_name}_{event.commit_sha}", {
            "result": result.model_dump(),
            "timestamp": result.timestamp.isoformat()
        })
        
        # Log completion
        if result.success:
            logger.info(
                "Webhook processing completed successfully",
                repository=event.repository_name,
                commit_sha=event.commit_sha,
                kustomizations_found=len(result.kustomization_paths),
                messages_published=result.messages_published,
                duration=result.duration_seconds
            )
        else:
            logger.error(
                "Webhook processing failed",
                repository=event.repository_name,
                commit_sha=event.commit_sha,
                error=result.error_message,
                duration=result.duration_seconds
            )
        
    except Exception as e:
        logger.error(
            "Background webhook processing failed",
            repository=event.repository_name,
            commit_sha=event.commit_sha,
            error=str(e),
            exc_info=True
        )
        
        # Store error in memory
        await mcp_ops.store_memory(f"webhook_{event.repository_name}_{event.commit_sha}", {
            "event": event.model_dump(),
            "status": "error",
            "error": str(e),
            "timestamp": event.timestamp.isoformat()
        })


@app.get("/health")
async def health_check() -> Dict[str, Any]:
    """Health check endpoint.
    
    Returns:
        Health status
    """
    try:
        # Test MCP connections
        await mcp_ops.store_memory("health_check", "ok")
        
        return await agent.health_check()
        
    except Exception as e:
        logger.error("Health check failed", error=str(e), exc_info=True)
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@app.get("/metrics")
async def get_metrics() -> Dict[str, Any]:
    """Get agent metrics.
    
    Returns:
        Agent metrics
    """
    try:
        base_metrics = await agent.get_metrics()
        
        # Add MCP-specific metrics
        mcp_metrics = {
            "mcp_servers_configured": len(agent.mcp_servers),
            "mcp_servers_enabled": len([s for s in agent.mcp_servers if s.enabled]),
            "active_mcp_sessions": len(mcp_ops.client.active_sessions)
        }
        
        return {**base_metrics, "mcp": mcp_metrics}
        
    except Exception as e:
        logger.error("Failed to get metrics", error=str(e), exc_info=True)
        return {"error": str(e)}


@app.get("/status/{repository_name}/{commit_sha}")
async def get_processing_status(repository_name: str, commit_sha: str) -> Dict[str, Any]:
    """Get processing status for a specific webhook event.
    
    Args:
        repository_name: Repository name
        commit_sha: Commit SHA
        
    Returns:
        Processing status
    """
    try:
        # Retrieve status from memory
        webhook_status = await mcp_ops.retrieve_memory(f"webhook_{repository_name}_{commit_sha}")
        result_status = await mcp_ops.retrieve_memory(f"result_{repository_name}_{commit_sha}")
        
        return {
            "repository": repository_name,
            "commit_sha": commit_sha,
            "webhook_status": webhook_status,
            "result": result_status
        }
        
    except Exception as e:
        logger.error(
            "Failed to get processing status",
            repository=repository_name,
            commit_sha=commit_sha,
            error=str(e),
            exc_info=True
        )
        return {"error": str(e)}


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "Kube2Helm Watcher Agent",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "webhook": "/webhook",
            "health": "/health",
            "metrics": "/metrics",
            "status": "/status/{repository_name}/{commit_sha}"
        }
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "agents.watcher.webhook_server:app",
        host=config.webhook_host,
        port=config.webhook_port,
        reload=False,
        log_level="info"
    )
