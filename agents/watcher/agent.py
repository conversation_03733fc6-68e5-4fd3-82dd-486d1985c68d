"""Main Watcher Agent implementation using MCP servers."""

import time
from datetime import datetime, timezone
from typing import List, Optional
from uuid import uuid4

from agents.watcher.models import WatcherConfig, WebhookEvent
from shared.config.settings import settings
from shared.config.mcp_servers import mcp_config
from shared.models.base import ConversionJob, JobStatus, RepositoryInfo
from shared.models.messages import ConvertKustomizeMessage, WatcherResult
from shared.utils.logging import get_logger, log_agent_complete, log_agent_error, log_agent_start
from shared.utils.pubsub import pubsub_client
from shared.utils.mcp_client import mcp_ops

logger = get_logger(__name__)


class WatcherAgent:
    """Agent that monitors repository changes and triggers conversion pipeline using MCP servers."""

    def __init__(self, config: Optional[WatcherConfig] = None):
        self.config = config or WatcherConfig()
        self.processing_status = {}  # Track processing status
        self.mcp_servers = mcp_config.get_servers_for_agent("watcher")
    
    async def process_webhook_event(self, event: WebhookEvent) -> WatcherResult:
        """Process a webhook event and trigger conversion jobs using MCP servers.

        Args:
            event: Webhook event to process

        Returns:
            Watcher agent result
        """
        job_id = uuid4()
        start_time = time.time()

        log_agent_start(
            logger,
            "watcher",
            str(job_id),
            repository_url=event.repository_url,
            commit_sha=event.commit_sha
        )

        try:
            # Store event in memory for tracking
            await mcp_ops.store_memory(f"webhook_event_{job_id}", {
                "event": event.model_dump(),
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

            # Analyze repository for Kustomize configurations using MCP
            kustomization_paths = await self._analyze_repository_with_mcp(event)

            if not kustomization_paths:
                logger.info(
                    "No valid Kustomize configurations found",
                    repository_url=event.repository_url
                )

                return WatcherResult(
                    job_id=job_id,
                    success=True,
                    duration_seconds=time.time() - start_time,
                    repository=self._create_repository_info(event),
                    kustomization_paths=[],
                    messages_published=0
                )
            
            # Create conversion jobs for each valid kustomization
            messages_published = 0

            for kustomization_path in kustomization_paths:
                
                # Create conversion job
                conversion_job = ConversionJob(
                    repository=self._create_repository_info(event),
                    kustomization_path=kustomization_path,
                    status=JobStatus.PENDING
                )

                # Create message to trigger conversion
                message = ConvertKustomizeMessage(
                    job_id=conversion_job.id,
                    repository=conversion_job.repository,
                    kustomization_path=kustomization_path,
                    webhook_payload=event.raw_payload
                )
                
                # Publish message to trigger parser agent
                try:
                    message_id = pubsub_client.publish_message(
                        settings.pubsub.topic_convert_kustomize,
                        message
                    )
                    
                    logger.info(
                        "Published conversion message",
                        job_id=str(conversion_job.id),
                        kustomization_path=kustomization_path,
                        message_id=message_id
                    )

                    messages_published += 1
                    
                except Exception as e:
                    logger.error(
                        "Failed to publish conversion message",
                        job_id=str(conversion_job.id),
                        kustomization_path=kustomization_path,
                        error=str(e),
                        exc_info=True
                    )
                    # Continue processing other kustomizations
            
            result = WatcherResult(
                job_id=job_id,
                success=True,
                duration_seconds=time.time() - start_time,
                repository=self._create_repository_info(event),
                kustomization_paths=kustomization_paths,
                messages_published=messages_published
            )
            
            log_agent_complete(
                logger,
                "watcher",
                str(job_id),
                result.duration_seconds,
                success=True,
                kustomizations_found=len(kustomization_paths),
                messages_published=messages_published
            )
            
            return result
            
        except Exception as e:
            log_agent_error(logger, "watcher", str(job_id), e)
            
            return WatcherResult(
                job_id=job_id,
                success=False,
                duration_seconds=time.time() - start_time,
                error_message=str(e),
                repository=self._create_repository_info(event),
                kustomization_paths=[],
                messages_published=0
            )

    async def _analyze_repository_with_mcp(self, event: WebhookEvent) -> List[str]:
        """Analyze repository for Kustomize configurations using MCP servers.

        Args:
            event: Webhook event

        Returns:
            List of valid kustomization file paths
        """
        try:
            # Create workspace directory
            workspace_path = f"{mcp_config.workspace_root}/{event.repository_name.replace('/', '_')}"

            # Clone repository using MCP Git server
            await mcp_ops.git_clone(
                repository_url=event.repository_url,
                target_path=workspace_path,
                branch=event.branch
            )

            # Find kustomization files using MCP filesystem server
            kustomization_files = []

            # Search for common kustomization file patterns
            patterns = ["kustomization.yaml", "kustomization.yml", "Kustomization"]

            for pattern in patterns:
                # Use filesystem server to search for files
                try:
                    search_result = await mcp_ops.client.call_tool("filesystem", "search_files", {
                        "directory": workspace_path,
                        "pattern": pattern,
                        "recursive": True
                    })

                    if search_result.get("files"):
                        kustomization_files.extend(search_result["files"])

                except Exception as e:
                    logger.warning(
                        "Failed to search for pattern",
                        pattern=pattern,
                        error=str(e)
                    )

            # Validate each kustomization file
            valid_kustomizations = []

            for file_path in kustomization_files:
                try:
                    # Read file content using MCP filesystem server
                    content = await mcp_ops.read_file(file_path)

                    # Basic validation - check if it's valid YAML and has kustomization structure
                    import yaml
                    kustomization_data = yaml.safe_load(content)

                    if isinstance(kustomization_data, dict) and (
                        'resources' in kustomization_data or
                        'bases' in kustomization_data or
                        'components' in kustomization_data
                    ):
                        # Convert absolute path to relative path
                        relative_path = file_path.replace(workspace_path + "/", "")
                        valid_kustomizations.append(relative_path)

                        logger.info(
                            "Found valid kustomization",
                            path=relative_path,
                            repository=event.repository_url
                        )

                except Exception as e:
                    logger.warning(
                        "Failed to validate kustomization file",
                        file_path=file_path,
                        error=str(e)
                    )

            # Store analysis results in memory
            await mcp_ops.store_memory(f"analysis_{event.commit_sha}", {
                "repository_url": event.repository_url,
                "branch": event.branch,
                "commit_sha": event.commit_sha,
                "kustomizations": valid_kustomizations,
                "workspace_path": workspace_path,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

            return valid_kustomizations

        except Exception as e:
            logger.error(
                "Repository analysis failed",
                repository_url=event.repository_url,
                error=str(e),
                exc_info=True
            )
            return []

    def _create_repository_info(self, event: WebhookEvent) -> RepositoryInfo:
        """Create RepositoryInfo from webhook event.
        
        Args:
            event: Webhook event
            
        Returns:
            Repository information
        """
        return RepositoryInfo(
            url=event.repository_url,
            branch=event.branch,
            commit_sha=event.commit_sha,
            author=event.author_name,
            message=event.commit_message,
            timestamp=event.timestamp,
            paths_changed=event.files_changed
        )
    
    async def health_check(self) -> dict:
        """Perform health check.
        
        Returns:
            Health status
        """
        try:
            # Test Pub/Sub connectivity
            topic_path = pubsub_client.get_topic_path(settings.pubsub.topic_convert_kustomize)
            
            return {
                "status": "healthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "pubsub_topic": topic_path,
                "config": {
                    "supported_patterns": self.config.supported_file_patterns,
                    "max_depth": self.config.max_analysis_depth,
                    "batch_processing": self.config.batch_processing
                }
            }
        except Exception as e:
            logger.error("Health check failed", error=str(e), exc_info=True)
            return {
                "status": "unhealthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": str(e)
            }
    
    async def get_metrics(self) -> dict:
        """Get agent metrics.
        
        Returns:
            Agent metrics
        """
        return {
            "processing_status_count": len(self.processing_status),
            "config": {
                "webhook_port": self.config.webhook_port,
                "batch_size": self.config.batch_size,
                "retry_attempts": self.config.retry_attempts,
                "analysis_timeout": self.config.analysis_timeout_seconds
            }
        }
