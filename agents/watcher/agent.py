"""Main Watcher Agent implementation."""

import time
from datetime import datetime, timezone
from typing import Optional
from uuid import uuid4

from agents.watcher.models import WatcherConfig, WebhookEvent
from agents.watcher.repository_analyzer import RepositoryAnalyzer
from shared.config.settings import settings
from shared.models.base import ConversionJob, JobStatus, RepositoryInfo
from shared.models.messages import ConvertKustomizeMessage, WatcherResult
from shared.utils.logging import get_logger, log_agent_complete, log_agent_error, log_agent_start
from shared.utils.pubsub import pubsub_client

logger = get_logger(__name__)


class WatcherAgent:
    """Agent that monitors repository changes and triggers conversion pipeline."""
    
    def __init__(self, config: Optional[WatcherConfig] = None):
        self.config = config or WatcherConfig()
        self.analyzer = RepositoryAnalyzer(self.config)
        self.processing_status = {}  # Track processing status
    
    async def process_webhook_event(self, event: WebhookEvent) -> WatcherResult:
        """Process a webhook event and trigger conversion jobs.
        
        Args:
            event: Webhook event to process
            
        Returns:
            Watcher agent result
        """
        job_id = uuid4()
        start_time = time.time()
        
        log_agent_start(
            logger,
            "watcher",
            str(job_id),
            repository_url=event.repository_url,
            commit_sha=event.commit_sha
        )
        
        try:
            # Analyze repository for Kustomize configurations
            analysis = self.analyzer.analyze_repository(event)
            
            if analysis.errors:
                error_message = "; ".join(analysis.errors)
                raise Exception(f"Repository analysis failed: {error_message}")
            
            if analysis.valid_kustomizations == 0:
                logger.info(
                    "No valid Kustomize configurations found",
                    repository_url=event.repository_url,
                    total_found=analysis.total_kustomizations
                )
                
                return WatcherResult(
                    job_id=job_id,
                    success=True,
                    duration_seconds=time.time() - start_time,
                    repository=self._create_repository_info(event),
                    kustomization_paths=[],
                    messages_published=0
                )
            
            # Create conversion jobs for each valid kustomization
            messages_published = 0
            kustomization_paths = []
            
            for kustomization in analysis.kustomizations:
                if not kustomization.is_valid:
                    logger.warning(
                        "Skipping invalid kustomization",
                        path=kustomization.path,
                        error=kustomization.error_message
                    )
                    continue
                
                # Create conversion job
                conversion_job = ConversionJob(
                    repository=self._create_repository_info(event),
                    kustomization_path=kustomization.path,
                    status=JobStatus.PENDING
                )
                
                # Create message to trigger conversion
                message = ConvertKustomizeMessage(
                    job_id=conversion_job.id,
                    repository=conversion_job.repository,
                    kustomization_path=kustomization.path,
                    webhook_payload=event.raw_payload
                )
                
                # Publish message to trigger parser agent
                try:
                    message_id = pubsub_client.publish_message(
                        settings.pubsub.topic_convert_kustomize,
                        message
                    )
                    
                    logger.info(
                        "Published conversion message",
                        job_id=str(conversion_job.id),
                        kustomization_path=kustomization.path,
                        message_id=message_id
                    )
                    
                    messages_published += 1
                    kustomization_paths.append(kustomization.path)
                    
                except Exception as e:
                    logger.error(
                        "Failed to publish conversion message",
                        job_id=str(conversion_job.id),
                        kustomization_path=kustomization.path,
                        error=str(e),
                        exc_info=True
                    )
                    # Continue processing other kustomizations
            
            result = WatcherResult(
                job_id=job_id,
                success=True,
                duration_seconds=time.time() - start_time,
                repository=self._create_repository_info(event),
                kustomization_paths=kustomization_paths,
                messages_published=messages_published
            )
            
            log_agent_complete(
                logger,
                "watcher",
                str(job_id),
                result.duration_seconds,
                success=True,
                kustomizations_found=len(kustomization_paths),
                messages_published=messages_published
            )
            
            return result
            
        except Exception as e:
            log_agent_error(logger, "watcher", str(job_id), e)
            
            return WatcherResult(
                job_id=job_id,
                success=False,
                duration_seconds=time.time() - start_time,
                error_message=str(e),
                repository=self._create_repository_info(event),
                kustomization_paths=[],
                messages_published=0
            )
    
    def _create_repository_info(self, event: WebhookEvent) -> RepositoryInfo:
        """Create RepositoryInfo from webhook event.
        
        Args:
            event: Webhook event
            
        Returns:
            Repository information
        """
        return RepositoryInfo(
            url=event.repository_url,
            branch=event.branch,
            commit_sha=event.commit_sha,
            author=event.author_name,
            message=event.commit_message,
            timestamp=event.timestamp,
            paths_changed=event.files_changed
        )
    
    async def health_check(self) -> dict:
        """Perform health check.
        
        Returns:
            Health status
        """
        try:
            # Test Pub/Sub connectivity
            topic_path = pubsub_client.get_topic_path(settings.pubsub.topic_convert_kustomize)
            
            return {
                "status": "healthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "pubsub_topic": topic_path,
                "config": {
                    "supported_patterns": self.config.supported_file_patterns,
                    "max_depth": self.config.max_analysis_depth,
                    "batch_processing": self.config.batch_processing
                }
            }
        except Exception as e:
            logger.error("Health check failed", error=str(e), exc_info=True)
            return {
                "status": "unhealthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": str(e)
            }
    
    async def get_metrics(self) -> dict:
        """Get agent metrics.
        
        Returns:
            Agent metrics
        """
        return {
            "processing_status_count": len(self.processing_status),
            "config": {
                "webhook_port": self.config.webhook_port,
                "batch_size": self.config.batch_size,
                "retry_attempts": self.config.retry_attempts,
                "analysis_timeout": self.config.analysis_timeout_seconds
            }
        }
