"""Repository analysis for detecting Kustomize configurations."""

import os
import tempfile
import time
from pathlib import Path
from typing import List, Optional

import git
import yaml
from git import Repo

from agents.watcher.models import (
    KustomizationDetection,
    RepositoryAnalysis,
    WatcherConfig,
    WebhookEvent,
)
from shared.utils.logging import get_logger

logger = get_logger(__name__)


class RepositoryAnalyzer:
    """Analyzes repositories for Kustomize configurations."""
    
    def __init__(self, config: WatcherConfig):
        self.config = config
    
    def clone_repository(
        self, 
        repository_url: str, 
        branch: str = "main",
        commit_sha: Optional[str] = None
    ) -> Repo:
        """Clone a repository to a temporary directory.
        
        Args:
            repository_url: Repository URL
            branch: Branch to clone
            commit_sha: Specific commit to checkout
            
        Returns:
            Git repository object
        """
        temp_dir = tempfile.mkdtemp(prefix="kube2helm_")
        
        try:
            logger.info(
                "Cloning repository",
                repository_url=repository_url,
                branch=branch,
                temp_dir=temp_dir
            )
            
            repo = Repo.clone_from(
                repository_url,
                temp_dir,
                branch=branch,
                depth=1  # Shallow clone for efficiency
            )
            
            # Checkout specific commit if provided
            if commit_sha:
                repo.git.checkout(commit_sha)
                logger.info("Checked out commit", commit_sha=commit_sha)
            
            return repo
            
        except Exception as e:
            logger.error(
                "Failed to clone repository",
                repository_url=repository_url,
                error=str(e),
                exc_info=True
            )
            # Clean up temp directory on failure
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            raise
    
    def find_kustomization_files(self, repo_path: str) -> List[str]:
        """Find all kustomization files in a repository.
        
        Args:
            repo_path: Path to repository
            
        Returns:
            List of kustomization file paths
        """
        kustomization_files = []
        repo_path_obj = Path(repo_path)
        
        for pattern in self.config.supported_file_patterns:
            # Use glob to find files matching the pattern
            for file_path in repo_path_obj.rglob(pattern):
                # Skip ignored paths
                relative_path = file_path.relative_to(repo_path_obj)
                if self._should_ignore_path(str(relative_path)):
                    continue
                
                # Check depth limit
                if len(relative_path.parts) > self.config.max_analysis_depth:
                    continue
                
                kustomization_files.append(str(relative_path))
        
        logger.info(
            "Found kustomization files",
            count=len(kustomization_files),
            files=kustomization_files
        )
        
        return kustomization_files
    
    def _should_ignore_path(self, path: str) -> bool:
        """Check if a path should be ignored.
        
        Args:
            path: File or directory path
            
        Returns:
            True if path should be ignored
        """
        for ignore_pattern in self.config.ignore_paths:
            if ignore_pattern in path:
                return True
        return False
    
    def analyze_kustomization(
        self, 
        repo_path: str, 
        kustomization_path: str
    ) -> KustomizationDetection:
        """Analyze a single kustomization file.
        
        Args:
            repo_path: Repository root path
            kustomization_path: Path to kustomization file
            
        Returns:
            Kustomization analysis result
        """
        full_path = os.path.join(repo_path, kustomization_path)
        base_directory = os.path.dirname(kustomization_path)
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                kustomization_data = yaml.safe_load(f)
            
            if not isinstance(kustomization_data, dict):
                return KustomizationDetection(
                    path=kustomization_path,
                    is_valid=False,
                    base_directory=base_directory,
                    error_message="Invalid YAML structure"
                )
            
            # Analyze kustomization content
            resources = kustomization_data.get('resources', [])
            bases = kustomization_data.get('bases', [])
            patches = kustomization_data.get('patches', []) + kustomization_data.get('patchesStrategicMerge', [])
            config_map_generator = kustomization_data.get('configMapGenerator', [])
            secret_generator = kustomization_data.get('secretGenerator', [])
            
            # Check for dependencies
            dependencies = []
            dependencies.extend(bases)
            
            # Check if resources exist
            resources_count = len(resources)
            for resource in resources:
                resource_path = os.path.join(os.path.dirname(full_path), resource)
                if not os.path.exists(resource_path):
                    logger.warning(
                        "Resource file not found",
                        kustomization=kustomization_path,
                        resource=resource,
                        resource_path=resource_path
                    )
            
            return KustomizationDetection(
                path=kustomization_path,
                is_valid=True,
                base_directory=base_directory,
                resources_count=resources_count,
                has_patches=len(patches) > 0,
                has_generators=len(config_map_generator) > 0 or len(secret_generator) > 0,
                dependencies=dependencies
            )
            
        except yaml.YAMLError as e:
            logger.error(
                "YAML parsing error",
                kustomization=kustomization_path,
                error=str(e)
            )
            return KustomizationDetection(
                path=kustomization_path,
                is_valid=False,
                base_directory=base_directory,
                error_message=f"YAML parsing error: {str(e)}"
            )
        
        except FileNotFoundError:
            logger.error(
                "Kustomization file not found",
                kustomization=kustomization_path,
                full_path=full_path
            )
            return KustomizationDetection(
                path=kustomization_path,
                is_valid=False,
                base_directory=base_directory,
                error_message="File not found"
            )
        
        except Exception as e:
            logger.error(
                "Error analyzing kustomization",
                kustomization=kustomization_path,
                error=str(e),
                exc_info=True
            )
            return KustomizationDetection(
                path=kustomization_path,
                is_valid=False,
                base_directory=base_directory,
                error_message=f"Analysis error: {str(e)}"
            )
    
    def analyze_repository(self, event: WebhookEvent) -> RepositoryAnalysis:
        """Analyze a repository for Kustomize configurations.
        
        Args:
            event: Webhook event containing repository information
            
        Returns:
            Repository analysis result
        """
        start_time = time.time()
        analysis = RepositoryAnalysis(
            repository_url=event.repository_url,
            branch=event.branch,
            commit_sha=event.commit_sha
        )
        
        repo = None
        try:
            # Clone repository
            repo = self.clone_repository(
                event.repository_url,
                event.branch,
                event.commit_sha
            )
            
            # Find kustomization files
            kustomization_files = self.find_kustomization_files(repo.working_dir)
            
            # Analyze each kustomization
            kustomizations = []
            for kustomization_path in kustomization_files:
                detection = self.analyze_kustomization(repo.working_dir, kustomization_path)
                kustomizations.append(detection)
            
            # Update analysis results
            analysis.kustomizations = kustomizations
            analysis.total_kustomizations = len(kustomizations)
            analysis.valid_kustomizations = sum(1 for k in kustomizations if k.is_valid)
            analysis.analysis_duration_seconds = time.time() - start_time
            
            logger.info(
                "Repository analysis completed",
                repository_url=event.repository_url,
                total_kustomizations=analysis.total_kustomizations,
                valid_kustomizations=analysis.valid_kustomizations,
                duration_seconds=analysis.analysis_duration_seconds
            )
            
        except Exception as e:
            error_message = f"Repository analysis failed: {str(e)}"
            analysis.errors.append(error_message)
            analysis.analysis_duration_seconds = time.time() - start_time
            
            logger.error(
                "Repository analysis failed",
                repository_url=event.repository_url,
                error=str(e),
                exc_info=True
            )
        
        finally:
            # Clean up cloned repository
            if repo:
                import shutil
                shutil.rmtree(repo.working_dir, ignore_errors=True)
        
        return analysis
