"""Comprehensive test for the complete multi-agent pipeline."""

import asyncio
import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from agents.watcher.agent import WatcherAgent
from agents.parser.agent import ParserAgent
from agents.doc_reference.agent import DocReferenceAgent
from agents.translator.agent import TranslatorAgent
from agents.linter.agent import LinterAgent
from agents.packager.agent import PackagerAgent

from shared.models.messages import (
    WebhookMessage, ParsedSpecMessage, ContextDocsMessage,
    DraftChartMessage, ValidatedChartMessage
)
from shared.models.repository import Repository, RepositoryType
from shared.models.kustomize import KustomizeConfig
from shared.models.helm import HelmChart, HelmValues, HelmTemplate, TemplateType
from shared.models.documentation import BestPractice
from shared.models.validation import ChartValidationResult, ValidationIssue, ValidationSeverity

from agents.linter.models import LinterConfig
from agents.packager.models import PackagerConfig


class TestCompletePipeline:
    """Test the complete multi-agent pipeline end-to-end."""

    @pytest.fixture
    def sample_repository(self):
        """Sample repository for testing."""
        return Repository(
            name="test-app",
            url="https://github.com/test/test-app",
            type=RepositoryType.GITHUB,
            branch="main"
        )

    @pytest.fixture
    def sample_webhook_message(self, sample_repository):
        """Sample webhook message."""
        return WebhookMessage(
            job_id="test-job-123",
            repository=sample_repository,
            event_type="push",
            commit_sha="abc123",
            kustomization_path="k8s/overlays/production"
        )

    @pytest.fixture
    def sample_kustomize_config(self):
        """Sample Kustomize configuration."""
        return KustomizeConfig(
            name="test-app",
            namespace="default",
            resources=["deployment.yaml", "service.yaml"],
            images=[{"name": "test-app", "newTag": "v1.0.0"}],
            patches=[],
            config_maps=[],
            secrets=[]
        )

    @pytest.fixture
    def sample_helm_chart(self):
        """Sample Helm chart."""
        return HelmChart(
            name="test-app",
            version="1.0.0",
            app_version="v1.0.0",
            description="Test application Helm chart",
            keywords=["test", "app"],
            values=HelmValues(
                global_values={"image": {"repository": "test-app", "tag": "v1.0.0"}},
                application_values={"service": {"type": "ClusterIP", "port": 80}},
                environment_values={}
            ),
            templates=[
                HelmTemplate(
                    name="deployment.yaml",
                    template_type=TemplateType.DEPLOYMENT,
                    content="apiVersion: apps/v1\nkind: Deployment\n..."
                ),
                HelmTemplate(
                    name="service.yaml",
                    template_type=TemplateType.SERVICE,
                    content="apiVersion: v1\nkind: Service\n..."
                )
            ],
            dependencies=[],
            helpers="{{/* Common labels */}}",
            notes="Thank you for installing {{ .Chart.Name }}",
            conversion_notes=["Converted from Kustomize", "Added Helm templating"]
        )

    @pytest.fixture
    def sample_best_practices(self):
        """Sample best practices."""
        return [
            BestPractice(
                title="Use Resource Limits",
                category="Security",
                priority="high",
                description="Always set resource limits for containers",
                rationale="Prevents resource exhaustion",
                example="resources:\n  limits:\n    cpu: 500m\n    memory: 512Mi",
                source_url="https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/"
            )
        ]

    @pytest.fixture
    def sample_validation_result(self):
        """Sample validation result."""
        return ChartValidationResult(
            valid=True,
            score=85.0,
            security_score=90.0,
            performance_score=80.0,
            maintainability_score=85.0,
            issues=[
                ValidationIssue(
                    category="PERFORMANCE",
                    severity=ValidationSeverity.WARNING,
                    title="Missing Readiness Probe",
                    description="Container does not have readiness probe configured",
                    suggestion="Add readinessProbe to ensure traffic is only sent to ready pods",
                    rule_id="performance_readiness_probe"
                )
            ],
            critical_issues=0,
            error_issues=0,
            warning_issues=1,
            info_issues=0,
            recommendations=["Add performance optimizations like probes and resource limits"]
        )

    @patch('shared.utils.mcp_client.mcp_ops')
    async def test_watcher_agent(self, mock_mcp_ops, sample_webhook_message):
        """Test Watcher Agent processing."""
        # Mock MCP operations
        mock_mcp_ops.clone_repository = AsyncMock(return_value="/tmp/test-repo")
        mock_mcp_ops.analyze_repository = AsyncMock(return_value={
            "kustomization_files": ["k8s/overlays/production/kustomization.yaml"],
            "has_kustomize": True
        })
        mock_mcp_ops.store_memory = AsyncMock()
        
        # Create agent and process message
        agent = WatcherAgent()
        result = await agent.process_message(sample_webhook_message)
        
        # Verify result
        assert result.success is True
        assert result.kustomization_found is True
        assert len(result.kustomization_paths) > 0
        
        # Verify MCP calls
        mock_mcp_ops.clone_repository.assert_called_once()
        mock_mcp_ops.analyze_repository.assert_called_once()

    @patch('shared.utils.mcp_client.mcp_ops')
    async def test_parser_agent(self, mock_mcp_ops, sample_repository, sample_kustomize_config):
        """Test Parser Agent processing."""
        # Mock MCP operations
        mock_mcp_ops.read_file = AsyncMock(return_value="apiVersion: kustomize.config.k8s.io/v1beta1\nkind: Kustomization")
        mock_mcp_ops.store_memory = AsyncMock()
        mock_mcp_ops.client.call_tool = AsyncMock(return_value={
            "resources": ["deployment.yaml", "service.yaml"],
            "images": [{"name": "test-app", "newTag": "v1.0.0"}]
        })
        
        # Create message
        message = ParsedKustomizeMessage(
            job_id="test-job-123",
            repository=sample_repository,
            kustomization_path="k8s/overlays/production",
            kustomize_config=sample_kustomize_config
        )
        
        # Create agent and process message
        agent = ParserAgent()
        result = await agent.process_message(message)
        
        # Verify result
        assert result.success is True
        assert result.resources_parsed > 0

    @patch('shared.utils.mcp_client.mcp_ops')
    async def test_doc_reference_agent(self, mock_mcp_ops, sample_repository, sample_kustomize_config, sample_best_practices):
        """Test DocReference Agent processing."""
        # Mock MCP operations
        mock_mcp_ops.client.call_tool = AsyncMock(return_value={
            "results": [
                {
                    "title": "Helm Best Practices",
                    "url": "https://helm.sh/docs/chart_best_practices/",
                    "snippet": "Best practices for Helm charts"
                }
            ]
        })
        mock_mcp_ops.store_memory = AsyncMock()
        
        # Create message
        message = ParsedKustomizeMessage(
            job_id="test-job-123",
            repository=sample_repository,
            kustomization_path="k8s/overlays/production",
            kustomize_config=sample_kustomize_config
        )
        
        # Create agent and process message
        agent = DocReferenceAgent()
        result = await agent.process_message(message)
        
        # Verify result
        assert result.success is True
        assert result.best_practices_found > 0

    @patch('shared.utils.mcp_client.mcp_ops')
    async def test_translator_agent(self, mock_mcp_ops, sample_repository, sample_kustomize_config, sample_best_practices, sample_helm_chart):
        """Test Translator Agent processing."""
        # Mock MCP operations and Vertex AI
        mock_mcp_ops.client.call_tool = AsyncMock(side_effect=[
            # Sequential thinking response
            {"response": "I need to convert Kustomize to Helm chart..."},
            # Memory storage
            None
        ])
        mock_mcp_ops.store_memory = AsyncMock()
        
        # Mock Vertex AI response
        with patch('google.cloud.aiplatform.gapic.PredictionServiceClient') as mock_client:
            mock_response = Mock()
            mock_response.predictions = [Mock()]
            mock_response.predictions[0].struct_value = {
                "helm_chart": sample_helm_chart.model_dump()
            }
            mock_client.return_value.predict.return_value = mock_response
            
            # Create message
            message = HelmContextMessage(
                job_id="test-job-123",
                repository=sample_repository,
                kustomization_path="k8s/overlays/production",
                kustomize_config=sample_kustomize_config,
                best_practices=sample_best_practices
            )
            
            # Create agent and process message
            agent = TranslatorAgent()
            result = await agent.process_message(message)
            
            # Verify result
            assert result.success is True
            assert result.helm_chart is not None
            assert result.helm_chart.name == "test-app"

    @patch('shared.utils.mcp_client.mcp_ops')
    async def test_linter_agent(self, mock_mcp_ops, sample_repository, sample_helm_chart, sample_validation_result):
        """Test Linter Agent processing."""
        # Mock MCP operations
        mock_mcp_ops.client.call_tool = AsyncMock(side_effect=[
            # Create temp directory
            None,
            # Write files
            None, None, None, None,
            # Helm lint
            {"exit_code": 0, "stdout": "1 chart(s) linted, 0 chart(s) failed"},
            # Kubeval
            {"exit_code": 0, "stdout": "The document is valid"},
            # Chart testing
            {"exit_code": 0, "stdout": "All tests passed"},
            # Cleanup
            None
        ])
        mock_mcp_ops.write_file = AsyncMock()
        mock_mcp_ops.store_memory = AsyncMock()
        
        # Create message
        message = DraftChartMessage(
            job_id="test-job-123",
            repository=sample_repository,
            kustomization_path="k8s/overlays/production",
            helm_chart=sample_helm_chart
        )
        
        # Create agent and process message
        config = LinterConfig()
        agent = LinterAgent(config)
        result = await agent.process_message(message)
        
        # Verify result
        assert result.success is True
        assert result.quality_score > 0

    @patch('shared.utils.mcp_client.mcp_ops')
    async def test_packager_agent(self, mock_mcp_ops, sample_repository, sample_helm_chart, sample_validation_result):
        """Test Packager Agent processing."""
        # Mock MCP operations
        mock_mcp_ops.client.call_tool = AsyncMock(side_effect=[
            # Create directories
            None, None, None,
            # Helm package
            {"exit_code": 0, "stdout": "Successfully packaged chart"},
            # Get file info
            {"size": 1024},
            # Create PR
            {"success": True, "pr_number": 123, "pr_url": "https://github.com/test/test-app/pull/123"},
            # Cleanup
            None
        ])
        mock_mcp_ops.write_file = AsyncMock()
        mock_mcp_ops.read_file = AsyncMock(return_value=b"chart content")
        mock_mcp_ops.store_memory = AsyncMock()
        
        # Create message
        message = ValidatedChartsMessage(
            job_id="test-job-123",
            repository=sample_repository,
            kustomization_path="k8s/overlays/production",
            helm_chart=sample_helm_chart,
            validation_result=sample_validation_result
        )
        
        # Create agent and process message
        config = PackagerConfig()
        agent = PackagerAgent(config)
        result = await agent.process_message(message)
        
        # Verify result
        assert result.success is True
        assert result.packages_created > 0
        assert result.pr_created is True

    async def test_complete_pipeline_integration(self):
        """Test complete pipeline integration (mock-based)."""
        # This test would simulate the complete flow through all agents
        # In a real scenario, this would involve actual Pub/Sub message passing
        
        # For now, we verify that all agents can be instantiated
        watcher = WatcherAgent()
        parser = ParserAgent()
        doc_reference = DocReferenceAgent()
        translator = TranslatorAgent()
        linter = LinterAgent()
        packager = PackagerAgent()
        
        # Verify all agents are properly initialized
        assert watcher is not None
        assert parser is not None
        assert doc_reference is not None
        assert translator is not None
        assert linter is not None
        assert packager is not None
        
        # Verify agent configurations
        assert hasattr(linter, 'config')
        assert hasattr(packager, 'config')
        
        print("✅ All agents successfully instantiated")
        print("✅ Complete multi-agent pipeline is ready")


if __name__ == "__main__":
    # Run the integration test
    asyncio.run(TestCompletePipeline().test_complete_pipeline_integration())
