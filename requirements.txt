# Core dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Google Cloud Platform
google-cloud-pubsub==2.18.4
google-cloud-storage==2.10.0
google-cloud-firestore==2.13.1
google-cloud-logging==3.8.0
google-cloud-bigquery==3.13.0
google-cloud-aiplatform==1.38.1
google-cloud-functions==1.16.0
google-cloud-build==3.21.0

# Kubernetes and Helm
kubernetes==28.1.0
pyyaml==6.0.1
helm-python==0.1.0

# MCP (Model Context Protocol) Python SDK
mcp==1.1.0

# HTTP and API clients
httpx==0.25.2
requests==2.31.0
aiohttp==3.9.1

# Data processing and validation
jsonschema==4.20.0
marshmallow==3.20.1
cerberus==1.3.5

# Utilities
click==8.1.7
rich==13.7.0
structlog==23.2.0
tenacity==8.2.3
jinja2==3.1.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-cov==4.1.0
httpx-mock==0.7.0

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Security
cryptography==41.0.8
python-jose[cryptography]==3.3.0

# Monitoring and observability
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-exporter-gcp-monitoring==1.6.0
